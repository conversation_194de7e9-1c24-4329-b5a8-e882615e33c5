// Checkout Management - ShopEase E-commerce Application

/**
 * Checkout Manager Class
 */
class CheckoutManager {
    constructor() {
        this.cartManager = null;
        this.orderData = {};
        this.isProcessing = false;
        this.init();
    }

    /**
     * Initialize checkout manager
     */
    init() {
        // Get cart manager instance
        this.cartManager = window.cartManager || new CartManager();
        
        // Load order summary
        this.loadOrderSummary();
        
        // Initialize form handlers
        this.initFormHandlers();
        
        // Initialize payment method handlers
        this.initPaymentHandlers();
        
        // Validate cart before checkout
        this.validateCart();
    }

    /**
     * Load order summary from cart
     */
    loadOrderSummary() {
        const orderItemsContainer = document.getElementById('order-items');
        const cartItems = this.cartManager.getItems();

        if (cartItems.length === 0) {
            this.redirectToCart();
            return;
        }

        // Render order items
        orderItemsContainer.innerHTML = cartItems.map(item => `
            <div class="order-item">
                <img src="${item.image}" alt="${item.name}" class="item-image">
                <div class="item-details">
                    <div class="item-name">${item.name}</div>
                    <div class="item-quantity">Qty: ${item.quantity}</div>
                </div>
                <div class="item-price">${Utils.formatCurrency(item.price * item.quantity)}</div>
            </div>
        `).join('');

        // Update totals
        this.updateOrderTotals();
    }

    /**
     * Update order totals
     */
    updateOrderTotals() {
        const subtotal = this.cartManager.getSubtotal();
        const shipping = this.cartManager.getShipping();
        const tax = this.cartManager.getTax();
        const total = this.cartManager.getFinalTotal();

        document.getElementById('subtotal').textContent = Utils.formatCurrency(subtotal);
        document.getElementById('shipping').textContent = shipping === 0 ? 'FREE' : Utils.formatCurrency(shipping);
        document.getElementById('tax').textContent = Utils.formatCurrency(tax);
        document.getElementById('total').textContent = Utils.formatCurrency(total);
    }

    /**
     * Initialize form handlers
     */
    initFormHandlers() {
        const checkoutForm = document.getElementById('checkout-form');
        
        if (checkoutForm) {
            checkoutForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.processOrder();
            });

            // Real-time validation
            const inputs = checkoutForm.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });

                input.addEventListener('input', () => {
                    this.clearFieldError(input);
                });
            });

            // Format card number input
            const cardNumberInput = checkoutForm.querySelector('input[name="cardNumber"]');
            if (cardNumberInput) {
                cardNumberInput.addEventListener('input', (e) => {
                    this.formatCardNumber(e.target);
                });
            }

            // Format expiry date input
            const expiryInput = checkoutForm.querySelector('input[name="expiryDate"]');
            if (expiryInput) {
                expiryInput.addEventListener('input', (e) => {
                    this.formatExpiryDate(e.target);
                });
            }

            // CVV input restriction
            const cvvInput = checkoutForm.querySelector('input[name="cvv"]');
            if (cvvInput) {
                cvvInput.addEventListener('input', (e) => {
                    e.target.value = e.target.value.replace(/\D/g, '').slice(0, 4);
                });
            }
        }
    }

    /**
     * Initialize payment method handlers
     */
    initPaymentHandlers() {
        const paymentMethodSelect = document.querySelector('select[name="paymentMethod"]');
        const cardDetailsSection = document.getElementById('card-details');

        if (paymentMethodSelect && cardDetailsSection) {
            paymentMethodSelect.addEventListener('change', (e) => {
                const isCardPayment = ['credit-card', 'debit-card'].includes(e.target.value);
                cardDetailsSection.style.display = isCardPayment ? 'block' : 'none';
                
                // Update required fields
                const cardInputs = cardDetailsSection.querySelectorAll('input');
                cardInputs.forEach(input => {
                    input.required = isCardPayment;
                });
            });
        }
    }

    /**
     * Validate cart before checkout
     */
    validateCart() {
        if (this.cartManager.isEmpty()) {
            this.redirectToCart();
            return false;
        }
        return true;
    }

    /**
     * Redirect to cart page
     */
    redirectToCart() {
        if (window.shopEaseApp) {
            window.shopEaseApp.showToast('Your cart is empty. Please add items before checkout.', 'warning');
        }
        setTimeout(() => {
            window.location.href = 'cart.html';
        }, 2000);
    }

    /**
     * Validate individual form field
     */
    validateField(field) {
        const value = field.value.trim();
        const fieldName = field.name;
        let isValid = true;
        let errorMessage = '';

        // Clear previous errors
        this.clearFieldError(field);

        // Required field validation
        if (field.required && !value) {
            isValid = false;
            errorMessage = 'This field is required';
        }

        // Specific field validations
        switch (fieldName) {
            case 'email':
                if (value && !Utils.isValidEmail(value)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid email address';
                }
                break;
            case 'phone':
                if (value && !Utils.isValidPhone(value)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid phone number';
                }
                break;
            case 'cardNumber':
                if (value && !this.isValidCardNumber(value)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid card number';
                }
                break;
            case 'expiryDate':
                if (value && !this.isValidExpiryDate(value)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid expiry date (MM/YY)';
                }
                break;
            case 'cvv':
                if (value && (value.length < 3 || value.length > 4)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid CVV';
                }
                break;
        }

        if (!isValid) {
            this.showFieldError(field, errorMessage);
        }

        return isValid;
    }

    /**
     * Show field error
     */
    showFieldError(field, message) {
        field.classList.add('error');
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        // Add new error message
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = message;
        errorElement.style.cssText = `
            color: var(--accent-color);
            font-size: var(--font-size-sm);
            margin-top: var(--spacing-xs);
        `;
        
        field.parentNode.appendChild(errorElement);
    }

    /**
     * Clear field error
     */
    clearFieldError(field) {
        field.classList.remove('error');
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    }

    /**
     * Format card number input
     */
    formatCardNumber(input) {
        let value = input.value.replace(/\D/g, '');
        value = value.replace(/(\d{4})(?=\d)/g, '$1 ');
        input.value = value.slice(0, 19); // Max 16 digits + 3 spaces
    }

    /**
     * Format expiry date input
     */
    formatExpiryDate(input) {
        let value = input.value.replace(/\D/g, '');
        if (value.length >= 2) {
            value = value.slice(0, 2) + '/' + value.slice(2, 4);
        }
        input.value = value;
    }

    /**
     * Validate card number using Luhn algorithm
     */
    isValidCardNumber(cardNumber) {
        const number = cardNumber.replace(/\s/g, '');
        if (!/^\d{13,19}$/.test(number)) return false;

        let sum = 0;
        let isEven = false;

        for (let i = number.length - 1; i >= 0; i--) {
            let digit = parseInt(number[i]);

            if (isEven) {
                digit *= 2;
                if (digit > 9) {
                    digit -= 9;
                }
            }

            sum += digit;
            isEven = !isEven;
        }

        return sum % 10 === 0;
    }

    /**
     * Validate expiry date
     */
    isValidExpiryDate(expiryDate) {
        const match = expiryDate.match(/^(\d{2})\/(\d{2})$/);
        if (!match) return false;

        const month = parseInt(match[1]);
        const year = parseInt('20' + match[2]);
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1;

        if (month < 1 || month > 12) return false;
        if (year < currentYear) return false;
        if (year === currentYear && month < currentMonth) return false;

        return true;
    }

    /**
     * Process the order
     */
    async processOrder() {
        if (this.isProcessing) return;

        const form = document.getElementById('checkout-form');
        const formData = new FormData(form);
        
        // Validate all fields
        const inputs = form.querySelectorAll('input[required], select[required]');
        let isFormValid = true;

        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isFormValid = false;
            }
        });

        if (!isFormValid) {
            if (window.shopEaseApp) {
                window.shopEaseApp.showToast('Please fix the errors in the form', 'error');
            }
            return;
        }

        this.isProcessing = true;
        const placeOrderBtn = document.getElementById('place-order-btn');
        
        // Update button state
        placeOrderBtn.disabled = true;
        placeOrderBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

        try {
            // Simulate order processing
            await Utils.wait(2000);

            // Create order object
            const orderData = {
                orderId: Utils.generateId(),
                items: this.cartManager.getItems(),
                shipping: this.getShippingData(formData),
                payment: this.getPaymentData(formData),
                totals: {
                    subtotal: this.cartManager.getSubtotal(),
                    shipping: this.cartManager.getShipping(),
                    tax: this.cartManager.getTax(),
                    total: this.cartManager.getFinalTotal()
                },
                orderDate: new Date().toISOString(),
                status: 'confirmed'
            };

            // Save order
            this.saveOrder(orderData);

            // Clear cart
            this.cartManager.clearCart();

            // Redirect to confirmation
            this.redirectToConfirmation(orderData.orderId);

        } catch (error) {
            console.error('Order processing error:', error);
            if (window.shopEaseApp) {
                window.shopEaseApp.showToast('Failed to process order. Please try again.', 'error');
            }
        } finally {
            this.isProcessing = false;
            placeOrderBtn.disabled = false;
            placeOrderBtn.innerHTML = '<i class="fas fa-lock"></i> Place Order';
        }
    }

    /**
     * Get shipping data from form
     */
    getShippingData(formData) {
        return {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            address: formData.get('address'),
            city: formData.get('city'),
            state: formData.get('state'),
            postalCode: formData.get('postalCode'),
            country: formData.get('country')
        };
    }

    /**
     * Get payment data from form (sanitized)
     */
    getPaymentData(formData) {
        const paymentMethod = formData.get('paymentMethod');
        const paymentData = { method: paymentMethod };

        if (['credit-card', 'debit-card'].includes(paymentMethod)) {
            const cardNumber = formData.get('cardNumber');
            paymentData.cardLast4 = cardNumber ? cardNumber.slice(-4) : '';
            paymentData.cardType = this.getCardType(cardNumber);
        }

        return paymentData;
    }

    /**
     * Get card type from card number
     */
    getCardType(cardNumber) {
        const number = cardNumber.replace(/\s/g, '');
        if (/^4/.test(number)) return 'Visa';
        if (/^5[1-5]/.test(number)) return 'Mastercard';
        if (/^3[47]/.test(number)) return 'American Express';
        return 'Unknown';
    }

    /**
     * Save order to storage
     */
    saveOrder(orderData) {
        const orders = StorageManager.get('user_orders', []);
        orders.unshift(orderData);
        
        // Keep only last 50 orders
        if (orders.length > 50) {
            orders.splice(50);
        }
        
        StorageManager.set('user_orders', orders);
        
        // Track order event
        if (this.cartManager) {
            this.cartManager.trackEvent('order_completed', {
                orderId: orderData.orderId,
                total: orderData.totals.total,
                itemCount: orderData.items.length
            });
        }
    }

    /**
     * Redirect to order confirmation
     */
    redirectToConfirmation(orderId) {
        if (window.shopEaseApp) {
            window.shopEaseApp.showToast('Order placed successfully!', 'success');
        }
        
        setTimeout(() => {
            window.location.href = `confirmation.html?order=${orderId}`;
        }, 1500);
    }
}

// Initialize CheckoutManager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (window.location.pathname.includes('checkout.html')) {
        window.checkoutManager = new CheckoutManager();
    }
});
