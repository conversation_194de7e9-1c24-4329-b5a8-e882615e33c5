/* Products Page Styles */

/* Products Container */
.products-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.products-header {
    text-align: center;
    margin-bottom: 3rem;
}

.products-header h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 1rem;
    font-weight: 700;
}

.products-header p {
    font-size: 1.1rem;
    color: #7f8c8d;
    max-width: 600px;
    margin: 0 auto;
}

/* Filter and Search Section */
.products-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 2px solid #e1e8ed;
    border-radius: 25px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
}

.filter-dropdown {
    position: relative;
}

.filter-dropdown select {
    padding: 0.75rem 2rem 0.75rem 1rem;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    background-color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-dropdown select:focus {
    outline: none;
    border-color: #3498db;
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

/* Product Card */
.product-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: #e74c3c;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
}

.product-badge.sale {
    background: #e74c3c;
}

.product-badge.new {
    background: #27ae60;
}

.product-badge.featured {
    background: #f39c12;
}

.product-info {
    padding: 1.5rem;
}

.product-category {
    color: #7f8c8d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.product-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.product-description {
    color: #7f8c8d;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-price {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.price-current {
    font-size: 1.4rem;
    font-weight: 700;
    color: #27ae60;
}

.price-original {
    font-size: 1rem;
    color: #7f8c8d;
    text-decoration: line-through;
    margin-left: 0.5rem;
}

.product-rating {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.stars {
    color: #f39c12;
    margin-right: 0.5rem;
}

.rating-text {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.product-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-add-cart {
    flex: 1;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-add-cart:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-1px);
}

.btn-wishlist {
    background: white;
    border: 2px solid #e1e8ed;
    color: #7f8c8d;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-wishlist:hover {
    border-color: #e74c3c;
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.btn-wishlist.active {
    border-color: #e74c3c;
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

/* Loading State */
.products-loading {
    text-align: center;
    padding: 3rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e1e8ed;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Empty State */
.products-empty {
    text-align: center;
    padding: 3rem;
    color: #7f8c8d;
}

.products-empty i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: #bdc3c7;
}

.products-empty h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 3rem;
}

.pagination button {
    padding: 0.5rem 1rem;
    border: 2px solid #e1e8ed;
    background: white;
    color: #7f8c8d;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination button:hover:not(:disabled) {
    border-color: #3498db;
    color: #3498db;
}

.pagination button.active {
    background: #3498db;
    border-color: #3498db;
    color: white;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}