# ShopEase E-commerce - Git Ignore File

# Operating System Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.sublime-project
.sublime-workspace

# Node.js (if using build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Build and Distribution
dist/
build/
.cache/
.parcel-cache/

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime Data
pids
*.pid
*.seed
*.pid.lock

# Coverage Directory
coverage/
*.lcov

# Dependency Directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Local development files
*.local

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar

# Database files (if any)
*.db
*.sqlite
*.sqlite3

# Cache files
.cache
*.cache

# Sass cache
.sass-cache/

# Compass
.compass/

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# Lock files (keep package-lock.json for consistency)
# Uncomment if you prefer not to commit lock files
# package-lock.json
# yarn.lock

# TypeScript cache
*.tsbuildinfo

# Optional stylelint cache
.stylelintcache

# Storybook build outputs
.out
.storybook-out

# Temporary files
.tmp/

# Local Netlify folder
.netlify

# Local Vercel folder
.vercel

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Custom project files to ignore
# Add any project-specific files here

# Development notes
NOTES.md
TODO.md
DEVELOPMENT.md

# Test files (if not needed in production)
# test/
# tests/
# __tests__/

# Documentation drafts
docs/drafts/

# Local configuration files
config.local.js
settings.local.json

# Deployment scripts (if sensitive)
# deploy.sh
# deploy.bat

# Keep these files for the project:
# !index.html
# !css/
# !js/
# !pages/
# !favicon.svg
# !manifest.webmanifest
# !README.md
# !DEPLOYMENT.md
