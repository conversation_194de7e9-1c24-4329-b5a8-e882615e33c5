<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Checkout - ShopEase</title>
    <meta name="description" content="Complete your purchase securely with ShopEase">
    
    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/svg+xml" href="../favicon.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="https://via.placeholder.com/32x32/3498db/ffffff?text=SE">
    <link rel="icon" type="image/png" sizes="16x16" href="https://via.placeholder.com/16x16/3498db/ffffff?text=SE">
    <link rel="apple-touch-icon" sizes="180x180" href="https://via.placeholder.com/180x180/3498db/ffffff?text=ShopEase">
    <meta name="theme-color" content="#3498db">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <link rel="stylesheet" href="../css/components.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Checkout Page Specific Styles -->
    <style>
        .checkout-page {
            min-height: 80vh;
            padding: var(--spacing-xl) 0;
            background: var(--bg-secondary);
        }
        
        .checkout-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
        }
        
        .checkout-header {
            text-align: center;
            margin-bottom: var(--spacing-xxl);
        }
        
        .checkout-header h1 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);
        }
        
        .checkout-steps {
            display: flex;
            justify-content: center;
            margin-bottom: var(--spacing-xxl);
            gap: var(--spacing-lg);
        }
        
        .step {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
        }
        
        .step.active {
            background: var(--primary-color);
            color: var(--white);
        }
        
        .step.completed {
            background: var(--success-color);
            color: var(--white);
        }
        
        .step-number {
            width: 24px;
            height: 24px;
            border-radius: var(--radius-full);
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: var(--font-size-sm);
        }
        
        .checkout-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--spacing-xxl);
        }
        
        .checkout-form {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xxl);
            box-shadow: var(--shadow-md);
        }
        
        .form-section {
            margin-bottom: var(--spacing-xxl);
        }
        
        .form-section h3 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
        }
        
        .form-row.full {
            grid-template-columns: 1fr;
        }
        
        .checkout-summary {
            position: sticky;
            top: var(--spacing-xl);
            height: fit-content;
        }
        
        .summary-card {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .order-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md) 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .order-item:last-child {
            border-bottom: none;
        }
        
        .item-image {
            width: 60px;
            height: 60px;
            border-radius: var(--radius-md);
            object-fit: cover;
        }
        
        .item-details {
            flex: 1;
        }
        
        .item-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .item-quantity {
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
        }
        
        .item-price {
            font-weight: 600;
            color: var(--success-color);
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm) 0;
        }
        
        .summary-row.total {
            font-size: var(--font-size-lg);
            font-weight: 700;
            color: var(--text-primary);
            border-top: 2px solid var(--light-gray);
            margin-top: var(--spacing-md);
            padding-top: var(--spacing-md);
        }
        
        .place-order-btn {
            width: 100%;
            background: var(--success-color);
            color: var(--white);
            border: none;
            padding: var(--spacing-lg);
            border-radius: var(--radius-md);
            font-size: var(--font-size-lg);
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-fast);
            margin-top: var(--spacing-lg);
        }
        
        .place-order-btn:hover {
            background: #229954;
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        
        .place-order-btn:disabled {
            background: var(--gray);
            cursor: not-allowed;
            transform: none;
        }
        
        .security-info {
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            margin-top: var(--spacing-lg);
            text-align: center;
        }
        
        .security-info i {
            color: var(--success-color);
            margin-right: var(--spacing-sm);
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .checkout-content {
                grid-template-columns: 1fr;
            }
            
            .checkout-steps {
                flex-direction: column;
                align-items: center;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .checkout-summary {
                position: static;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p>Loading Checkout...</p>
    </div>

    <!-- Header Section -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <h1><i class="fas fa-shopping-bag"></i> ShopEase</h1>
                </div>
                
                <div class="nav-menu" id="nav-menu" aria-hidden="true">
                    <a href="../index.html" class="nav-link">Home</a>
                    <a href="products.html" class="nav-link">Products</a>
                    <a href="cart.html" class="nav-link">
                        <i class="fas fa-shopping-cart"></i>
                        Cart <span class="cart-count" id="cart-count">0</span>
                    </a>
                    <a href="profile.html" class="nav-link">
                        <i class="fas fa-user"></i>
                        Profile
                    </a>
                </div>
                
                <div class="nav-actions">
                    <button type="button" class="search-toggle" id="search-toggle">
                        <i class="fas fa-search"></i>
                    </button>
                    <button type="button" class="theme-toggle" id="theme-toggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button type="button" class="mobile-menu-toggle" id="mobile-menu-toggle" aria-expanded="false" aria-controls="nav-menu" aria-label="Toggle navigation menu">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
            
            <!-- Search Bar -->
            <div class="search-bar" id="search-bar">
                <div class="search-container">
                    <input type="text" placeholder="Search products..." id="search-input">
                    <button type="button" class="search-btn" id="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Checkout Page Content -->
    <main class="checkout-page">
        <div class="checkout-container">
            <!-- Checkout Header -->
            <div class="checkout-header">
                <h1>Secure Checkout</h1>
                <p>Complete your order safely and securely</p>
            </div>

            <!-- Checkout Steps -->
            <div class="checkout-steps">
                <div class="step completed">
                    <div class="step-number">1</div>
                    <span>Cart Review</span>
                </div>
                <div class="step active">
                    <div class="step-number">2</div>
                    <span>Checkout</span>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <span>Confirmation</span>
                </div>
            </div>

            <!-- Checkout Content -->
            <div class="checkout-content">
                <!-- Checkout Form -->
                <div class="checkout-form">
                    <form id="checkout-form">
                        <!-- Shipping Information -->
                        <div class="form-section">
                            <h3>
                                <i class="fas fa-shipping-fast"></i>
                                Shipping Information
                            </h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">First Name *</label>
                                    <input type="text" class="form-input" name="firstName" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Last Name *</label>
                                    <input type="text" class="form-input" name="lastName" required>
                                </div>
                            </div>
                            <div class="form-row full">
                                <div class="form-group">
                                    <label class="form-label">Email Address *</label>
                                    <input type="email" class="form-input" name="email" required>
                                </div>
                            </div>
                            <div class="form-row full">
                                <div class="form-group">
                                    <label class="form-label">Phone Number *</label>
                                    <input type="tel" class="form-input" name="phone" required>
                                </div>
                            </div>
                            <div class="form-row full">
                                <div class="form-group">
                                    <label class="form-label">Address *</label>
                                    <input type="text" class="form-input" name="address" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">City *</label>
                                    <input type="text" class="form-input" name="city" required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Postal Code *</label>
                                    <input type="text" class="form-input" name="postalCode" required>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label class="form-label">Country *</label>
                                    <select class="form-select" name="country" required>
                                        <option value="">Select Country</option>
                                        <option value="KE">Kenya</option>
                                        <option value="US">United States</option>
                                        <option value="UK">United Kingdom</option>
                                        <option value="CA">Canada</option>
                                        <option value="AU">Australia</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">State/Province</label>
                                    <input type="text" class="form-input" name="state">
                                </div>
                            </div>
                        </div>

                        <!-- Payment Information -->
                        <div class="form-section">
                            <h3>
                                <i class="fas fa-credit-card"></i>
                                Payment Information
                            </h3>
                            <div class="form-row full">
                                <div class="form-group">
                                    <label class="form-label">Payment Method *</label>
                                    <select class="form-select" name="paymentMethod" required>
                                        <option value="">Select Payment Method</option>
                                        <option value="credit-card">Credit Card</option>
                                        <option value="debit-card">Debit Card</option>
                                        <option value="paypal">PayPal</option>
                                        <option value="mpesa">M-Pesa</option>
                                    </select>
                                </div>
                            </div>
                            <div id="card-details" style="display: none;">
                                <div class="form-row full">
                                    <div class="form-group">
                                        <label class="form-label">Card Number *</label>
                                        <input type="text" class="form-input" name="cardNumber" placeholder="1234 5678 9012 3456">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">Expiry Date *</label>
                                        <input type="text" class="form-input" name="expiryDate" placeholder="MM/YY">
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">CVV *</label>
                                        <input type="text" class="form-input" name="cvv" placeholder="123">
                                    </div>
                                </div>
                                <div class="form-row full">
                                    <div class="form-group">
                                        <label class="form-label">Cardholder Name *</label>
                                        <input type="text" class="form-input" name="cardholderName">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Order Notes -->
                        <div class="form-section">
                            <h3>
                                <i class="fas fa-sticky-note"></i>
                                Order Notes (Optional)
                            </h3>
                            <div class="form-row full">
                                <div class="form-group">
                                    <label class="form-label">Special Instructions</label>
                                    <textarea class="form-textarea" name="orderNotes" rows="3" placeholder="Any special delivery instructions..."></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Order Summary -->
                <div class="checkout-summary">
                    <div class="summary-card">
                        <h3>Order Summary</h3>
                        <div id="order-items">
                            <!-- Order items will be loaded here -->
                        </div>
                        <div class="summary-totals">
                            <div class="summary-row">
                                <span>Subtotal</span>
                                <span id="subtotal">$0.00</span>
                            </div>
                            <div class="summary-row">
                                <span>Shipping</span>
                                <span id="shipping">$0.00</span>
                            </div>
                            <div class="summary-row">
                                <span>Tax</span>
                                <span id="tax">$0.00</span>
                            </div>
                            <div class="summary-row total">
                                <span>Total</span>
                                <span id="total">$0.00</span>
                            </div>
                        </div>
                        <button type="submit" form="checkout-form" class="place-order-btn" id="place-order-btn">
                            <i class="fas fa-lock"></i>
                            Place Order
                        </button>
                        <div class="security-info">
                            <i class="fas fa-shield-alt"></i>
                            Your payment information is secure and encrypted
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>ShopEase</h3>
                    <p>Your trusted online shopping destination for quality products at amazing prices.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="cart.html">Cart</a></li>
                        <li><a href="profile.html">Profile</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 ShopEase. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="back-to-top" aria-label="Back to top">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toast-container"></div>

    <!-- JavaScript Files -->
    <script src="../js/utils.js"></script>
    <script src="../js/main.js"></script>
    <script src="../js/products.js"></script>
    <script src="../js/cart.js"></script>
    <script src="../js/checkout.js"></script>
</body>
</html>
