<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - ShopEase</title>
    <meta name="description" content="Manage your account, orders, and preferences">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <link rel="stylesheet" href="../css/components.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Profile Page Specific Styles -->
    <style>
        .profile-page {
            min-height: 80vh;
            padding: var(--spacing-xl) 0;
            background: var(--bg-secondary);
        }
        
        .profile-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: var(--white);
            padding: var(--spacing-xxl);
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-xl);
            text-align: center;
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: var(--radius-full);
            border: 4px solid rgba(255, 255, 255, 0.3);
            margin: 0 auto var(--spacing-lg);
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
        }
        
        .profile-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: var(--spacing-xl);
        }
        
        .profile-sidebar {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }
        
        .profile-card {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
        }
        
        .profile-card h3 {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .quick-actions {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
            background: var(--bg-secondary);
            border: none;
            border-radius: var(--radius-md);
            color: var(--text-primary);
            text-decoration: none;
            transition: all var(--transition-fast);
            cursor: pointer;
        }
        
        .action-btn:hover {
            background: var(--primary-color);
            color: var(--white);
            transform: translateX(5px);
        }
        
        .profile-main {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-sm);
        }
        
        .tabs {
            margin-bottom: var(--spacing-xl);
        }
        
        .tab-list {
            display: flex;
            border-bottom: 2px solid var(--light-gray);
            margin-bottom: var(--spacing-lg);
        }
        
        .tab-button {
            background: none;
            border: none;
            padding: var(--spacing-md) var(--spacing-lg);
            cursor: pointer;
            color: var(--text-secondary);
            font-weight: 500;
            border-bottom: 2px solid transparent;
            transition: all var(--transition-fast);
        }
        
        .tab-button:hover {
            color: var(--text-primary);
        }
        
        .tab-button.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md) 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .info-label {
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .info-value {
            color: var(--text-primary);
            font-weight: 600;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }
        
        .stat-card {
            background: var(--bg-secondary);
            padding: var(--spacing-lg);
            border-radius: var(--radius-lg);
            text-align: center;
        }
        
        .stat-number {
            font-size: var(--font-size-3xl);
            font-weight: 700;
            color: var(--primary-color);
            display: block;
            margin-bottom: var(--spacing-sm);
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .form-help {
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
            margin-top: var(--spacing-xs);
            display: block;
        }

        .checkbox-group {
            margin-top: var(--spacing-sm);
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            cursor: pointer;
            font-weight: 500;
        }

        .checkbox-label input[type="checkbox"] {
            width: auto;
            margin: 0;
        }

        .checkmark {
            width: 20px;
            height: 20px;
            border: 2px solid var(--light-gray);
            border-radius: var(--radius-sm);
            position: relative;
            transition: all var(--transition-fast);
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .checkbox-label input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .form-actions {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-lg);
            border-top: 1px solid var(--light-gray);
        }

        .form-actions .btn {
            flex: 1;
        }

        @media (max-width: 768px) {
            .profile-content {
                grid-template-columns: 1fr;
            }

            .tab-list {
                flex-wrap: wrap;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .form-actions {
                flex-direction: column;
            }

            .form-actions .btn {
                flex: none;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p>Loading Profile...</p>
    </div>

    <!-- Header Section -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <h1><i class="fas fa-shopping-bag"></i> ShopEase</h1>
                </div>
                
                <div class="nav-menu" id="nav-menu">
                    <a href="../index.html" class="nav-link">Home</a>
                    <a href="products.html" class="nav-link">Products</a>
                    <a href="cart.html" class="nav-link">
                        <i class="fas fa-shopping-cart"></i>
                        Cart <span class="cart-count" id="cart-count">0</span>
                    </a>
                    <a href="profile.html" class="nav-link active">
                        <i class="fas fa-user"></i>
                        Profile
                    </a>
                </div>
                
                <div class="nav-actions">
                    <button class="search-toggle" id="search-toggle">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="theme-toggle" id="theme-toggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
            
            <!-- Search Bar -->
            <div class="search-bar" id="search-bar">
                <div class="search-container">
                    <input type="text" placeholder="Search products..." id="search-input">
                    <button class="search-btn" id="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Profile Page Content -->
    <main class="profile-page">
        <div class="container">
            <!-- Profile Header -->
            <div class="profile-header">
                <div class="profile-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <h1>Welcome, Guest User!</h1>
                <p>Manage your account and track your orders</p>
            </div>

            <!-- Profile Content -->
            <div class="profile-content">
                <!-- Sidebar -->
                <div class="profile-sidebar">
                    <div class="profile-card">
                        <h3><i class="fas fa-chart-bar"></i> Account Stats</h3>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <span class="stat-number" id="total-orders">0</span>
                                <span class="stat-label">Total Orders</span>
                            </div>
                            <div class="stat-card">
                                <span class="stat-number" id="wishlist-items">0</span>
                                <span class="stat-label">Wishlist Items</span>
                            </div>
                        </div>
                    </div>

                    <div class="profile-card">
                        <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
                        <div class="quick-actions">
                            <a href="products.html" class="action-btn">
                                <i class="fas fa-shopping-bag"></i>
                                Browse Products
                            </a>
                            <a href="cart.html" class="action-btn">
                                <i class="fas fa-shopping-cart"></i>
                                View Cart
                            </a>
                            <button class="action-btn" onclick="clearData()">
                                <i class="fas fa-trash"></i>
                                Clear All Data
                            </button>
                            <button class="action-btn" onclick="exportData()">
                                <i class="fas fa-download"></i>
                                Export Data
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="profile-main">
                    <div class="tabs">
                        <div class="tab-list">
                            <button class="tab-button active" data-tab="overview">Overview</button>
                            <button class="tab-button" data-tab="orders">Orders</button>
                            <button class="tab-button" data-tab="wishlist">Wishlist</button>
                            <button class="tab-button" data-tab="settings">Settings</button>
                        </div>

                        <!-- Overview Tab -->
                        <div class="tab-content active" id="overview">
                            <h3>Account Overview</h3>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-label">Account Type</span>
                                    <span class="info-value">Guest User</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Member Since</span>
                                    <span class="info-value" id="member-since">Today</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Cart Items</span>
                                    <span class="info-value" id="cart-items-count">0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Theme</span>
                                    <span class="info-value" id="current-theme">Auto</span>
                                </div>
                            </div>
                        </div>

                        <!-- Orders Tab -->
                        <div class="tab-content" id="orders">
                            <h3>Order History</h3>
                            <div class="empty-state">
                                <div class="empty-state-icon">
                                    <i class="fas fa-receipt"></i>
                                </div>
                                <h4 class="empty-state-title">No orders yet</h4>
                                <p class="empty-state-description">
                                    You haven't placed any orders yet. Start shopping to see your order history here.
                                </p>
                                <a href="products.html" class="btn btn-primary">
                                    Start Shopping
                                </a>
                            </div>
                        </div>

                        <!-- Wishlist Tab -->
                        <div class="tab-content" id="wishlist">
                            <h3>My Wishlist</h3>
                            <div id="wishlist-content">
                                <div class="empty-state">
                                    <div class="empty-state-icon">
                                        <i class="fas fa-heart"></i>
                                    </div>
                                    <h4 class="empty-state-title">Your wishlist is empty</h4>
                                    <p class="empty-state-description">
                                        Save items you love for later by clicking the heart icon on products.
                                    </p>
                                    <a href="products.html" class="btn btn-primary">
                                        Browse Products
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Settings Tab -->
                        <div class="tab-content" id="settings">
                            <h3>Settings</h3>
                            <form id="settings-form">
                                <div class="form-group">
                                    <label class="form-label">Theme Preference</label>
                                    <select class="form-select" id="theme-select" name="theme">
                                        <option value="auto">Auto (System)</option>
                                        <option value="light">Light</option>
                                        <option value="dark">Dark</option>
                                        <option value="high-contrast">High Contrast</option>
                                    </select>
                                    <small class="form-help">Choose your preferred color scheme</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Notifications</label>
                                    <div class="checkbox-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="notifications" name="notifications">
                                            <span class="checkmark"></span>
                                            Enable notifications
                                        </label>
                                    </div>
                                    <small class="form-help">Receive updates about your orders and promotions</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Animations</label>
                                    <div class="checkbox-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="animations" name="animations">
                                            <span class="checkmark"></span>
                                            Enable animations
                                        </label>
                                    </div>
                                    <small class="form-help">Show smooth transitions and animations</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Language</label>
                                    <select class="form-select" id="language-select" name="language">
                                        <option value="en">English</option>
                                        <option value="es">Español</option>
                                        <option value="fr">Français</option>
                                        <option value="de">Deutsch</option>
                                        <option value="sw">Kiswahili</option>
                                    </select>
                                    <small class="form-help">Choose your preferred language</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">Currency</label>
                                    <select class="form-select" id="currency-select" name="currency">
                                        <option value="USD">USD ($)</option>
                                        <option value="EUR">EUR (€)</option>
                                        <option value="GBP">GBP (£)</option>
                                        <option value="KES">KES (KSh)</option>
                                        <option value="JPY">JPY (¥)</option>
                                    </select>
                                    <small class="form-help">Choose your preferred currency for prices</small>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i>
                                        Save Settings
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="resetSettings()">
                                        <i class="fas fa-undo"></i>
                                        Reset to Defaults
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>ShopEase</h3>
                    <p>Your trusted online shopping destination for quality products at amazing prices.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="cart.html">Cart</a></li>
                        <li><a href="profile.html">Profile</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 ShopEase. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="back-to-top" aria-label="Back to top">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toast-container"></div>

    <!-- JavaScript Files -->
    <script src="../js/utils.js"></script>
    <script src="../js/main.js"></script>
    <script src="../js/products.js"></script>
    <script src="../js/cart.js"></script>
    
    <!-- Profile Page Script -->
    <script>
        // Profile page functionality
        document.addEventListener('DOMContentLoaded', () => {
            initProfilePage();
        });

        function initProfilePage() {
            // Initialize tabs
            initTabs();

            // Initialize settings form
            initSettingsForm();

            // Load profile data
            loadProfileData();

            // Update stats
            updateStats();

            // Listen for settings changes from main app
            window.addEventListener('settingsChanged', (e) => {
                loadProfileData();
                updateStats();
            });
        }

        function initTabs() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.getAttribute('data-tab');
                    
                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));
                    
                    // Add active class to clicked button and corresponding content
                    button.classList.add('active');
                    document.getElementById(tabId).classList.add('active');
                });
            });
        }

        function loadProfileData() {
            // Get current settings from main app
            const settings = window.shopEaseApp ? window.shopEaseApp.getSettings() : StorageManager.get('user_settings', {
                theme: 'auto',
                notifications: true,
                animations: true,
                language: 'en',
                currency: 'USD'
            });

            // Load form values
            document.getElementById('theme-select').value = settings.theme;
            document.getElementById('notifications').checked = settings.notifications;
            document.getElementById('animations').checked = settings.animations;
            document.getElementById('language-select').value = settings.language;
            document.getElementById('currency-select').value = settings.currency;

            // Update overview display
            document.getElementById('current-theme').textContent = Utils.capitalize(settings.theme);

            // Set member since date
            const memberSince = StorageManager.get('member_since', new Date().toISOString());
            if (!StorageManager.get('member_since')) {
                StorageManager.set('member_since', new Date().toISOString());
            }
            document.getElementById('member-since').textContent = Utils.formatDate(memberSince);
        }

        function updateStats() {
            // Update cart items count
            if (window.cartManager) {
                document.getElementById('cart-items-count').textContent = window.cartManager.getItemCount();
            }

            // Update wishlist count
            const wishlist = JSON.parse(localStorage.getItem('wishlist') || '[]');
            document.getElementById('wishlist-items').textContent = wishlist.length;

            // Update total orders (placeholder)
            document.getElementById('total-orders').textContent = '0';
        }

        function initSettingsForm() {
            const settingsForm = document.getElementById('settings-form');

            if (settingsForm) {
                settingsForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    saveSettings();
                });

                // Real-time theme preview
                const themeSelect = document.getElementById('theme-select');
                themeSelect.addEventListener('change', (e) => {
                    if (window.shopEaseApp) {
                        window.shopEaseApp.updateSetting('theme', e.target.value);
                    }
                });

                // Real-time settings updates
                const checkboxes = settingsForm.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', (e) => {
                        if (window.shopEaseApp) {
                            window.shopEaseApp.updateSetting(e.target.name, e.target.checked);
                        }
                    });
                });

                const selects = settingsForm.querySelectorAll('select');
                selects.forEach(select => {
                    select.addEventListener('change', (e) => {
                        if (window.shopEaseApp && e.target.name !== 'theme') {
                            window.shopEaseApp.updateSetting(e.target.name, e.target.value);
                        }
                    });
                });
            }
        }

        function saveSettings() {
            const form = document.getElementById('settings-form');
            const formData = new FormData(form);

            const settings = {
                theme: formData.get('theme'),
                notifications: formData.has('notifications'),
                animations: formData.has('animations'),
                language: formData.get('language'),
                currency: formData.get('currency')
            };

            if (window.shopEaseApp) {
                // Update all settings
                Object.entries(settings).forEach(([key, value]) => {
                    window.shopEaseApp.updateSetting(key, value);
                });

                window.shopEaseApp.showToast('Settings saved successfully!', 'success');
            } else {
                // Fallback if main app not available
                StorageManager.set('user_settings', settings);
                showToast('Settings saved successfully!', 'success');
            }

            // Update overview display
            document.getElementById('current-theme').textContent = Utils.capitalize(settings.theme);

            // Update stats
            updateStats();
        }

        function resetSettings() {
            if (confirm('Are you sure you want to reset all settings to defaults?')) {
                const defaultSettings = {
                    theme: 'auto',
                    notifications: true,
                    animations: true,
                    language: 'en',
                    currency: 'USD'
                };

                if (window.shopEaseApp) {
                    Object.entries(defaultSettings).forEach(([key, value]) => {
                        window.shopEaseApp.updateSetting(key, value);
                    });
                    window.shopEaseApp.showToast('Settings reset to defaults', 'info');
                } else {
                    StorageManager.set('user_settings', defaultSettings);
                    showToast('Settings reset to defaults', 'info');
                }

                // Reload form data
                loadProfileData();
            }
        }

        function clearData() {
            if (confirm('Are you sure you want to clear all data? This will remove your cart, wishlist, and preferences.')) {
                localStorage.clear();
                if (window.shopEaseApp) {
                    window.shopEaseApp.showToast('All data cleared', 'info');
                    setTimeout(() => location.reload(), 1000);
                }
            }
        }

        function exportData() {
            const data = {
                cart: JSON.parse(localStorage.getItem('shopease_cart') || '[]'),
                wishlist: JSON.parse(localStorage.getItem('wishlist') || '[]'),
                theme: localStorage.getItem('theme') || 'auto',
                memberSince: localStorage.getItem('member_since') || new Date().toISOString(),
                exportedAt: new Date().toISOString()
            };

            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            Utils.downloadFile(url, `shopease-profile-${new Date().toISOString().split('T')[0]}.json`);
            
            URL.revokeObjectURL(url);
            
            if (window.shopEaseApp) {
                window.shopEaseApp.showToast('Profile data exported!', 'success');
            } else {
                showToast('Profile data exported!', 'success');
            }
        }

        // Simple toast function for fallback
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--bg-primary);
                color: var(--text-primary);
                padding: 1rem 1.5rem;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border-left: 4px solid var(--primary-color);
                z-index: 10000;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(() => {
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
