<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - ShopEase</title>
    <meta name="description" content="Manage your account, orders, and preferences">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <link rel="stylesheet" href="../css/components.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Profile Page Specific Styles -->
    <style>
        .profile-page {
            min-height: 80vh;
            padding: var(--spacing-xl) 0;
            background: var(--bg-secondary);
        }
        
        .profile-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: var(--white);
            padding: var(--spacing-xxl);
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-xl);
            text-align: center;
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: var(--radius-full);
            border: 4px solid rgba(255, 255, 255, 0.3);
            margin: 0 auto var(--spacing-lg);
            background: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
        }
        
        .profile-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: var(--spacing-xl);
        }
        
        .profile-sidebar {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }
        
        .profile-card {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
        }
        
        .profile-card h3 {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .quick-actions {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-md);
            background: var(--bg-secondary);
            border: none;
            border-radius: var(--radius-md);
            color: var(--text-primary);
            text-decoration: none;
            transition: all var(--transition-fast);
            cursor: pointer;
        }
        
        .action-btn:hover {
            background: var(--primary-color);
            color: var(--white);
            transform: translateX(5px);
        }
        
        .profile-main {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-sm);
        }
        
        .tabs {
            margin-bottom: var(--spacing-xl);
        }
        
        .tab-list {
            display: flex;
            border-bottom: 2px solid var(--light-gray);
            margin-bottom: var(--spacing-lg);
        }
        
        .tab-button {
            background: none;
            border: none;
            padding: var(--spacing-md) var(--spacing-lg);
            cursor: pointer;
            color: var(--text-secondary);
            font-weight: 500;
            border-bottom: 2px solid transparent;
            transition: all var(--transition-fast);
        }
        
        .tab-button:hover {
            color: var(--text-primary);
        }
        
        .tab-button.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md) 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .info-label {
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .info-value {
            color: var(--text-primary);
            font-weight: 600;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }
        
        .stat-card {
            background: var(--bg-secondary);
            padding: var(--spacing-lg);
            border-radius: var(--radius-lg);
            text-align: center;
        }
        
        .stat-number {
            font-size: var(--font-size-3xl);
            font-weight: 700;
            color: var(--primary-color);
            display: block;
            margin-bottom: var(--spacing-sm);
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        @media (max-width: 768px) {
            .profile-content {
                grid-template-columns: 1fr;
            }
            
            .tab-list {
                flex-wrap: wrap;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p>Loading Profile...</p>
    </div>

    <!-- Header Section -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <h1><i class="fas fa-shopping-bag"></i> ShopEase</h1>
                </div>
                
                <div class="nav-menu" id="nav-menu">
                    <a href="../index.html" class="nav-link">Home</a>
                    <a href="products.html" class="nav-link">Products</a>
                    <a href="cart.html" class="nav-link">
                        <i class="fas fa-shopping-cart"></i>
                        Cart <span class="cart-count" id="cart-count">0</span>
                    </a>
                    <a href="profile.html" class="nav-link active">
                        <i class="fas fa-user"></i>
                        Profile
                    </a>
                </div>
                
                <div class="nav-actions">
                    <button class="search-toggle" id="search-toggle">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="theme-toggle" id="theme-toggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
            
            <!-- Search Bar -->
            <div class="search-bar" id="search-bar">
                <div class="search-container">
                    <input type="text" placeholder="Search products..." id="search-input">
                    <button class="search-btn" id="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Profile Page Content -->
    <main class="profile-page">
        <div class="container">
            <!-- Profile Header -->
            <div class="profile-header">
                <div class="profile-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <h1>Welcome, Guest User!</h1>
                <p>Manage your account and track your orders</p>
            </div>

            <!-- Profile Content -->
            <div class="profile-content">
                <!-- Sidebar -->
                <div class="profile-sidebar">
                    <div class="profile-card">
                        <h3><i class="fas fa-chart-bar"></i> Account Stats</h3>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <span class="stat-number" id="total-orders">0</span>
                                <span class="stat-label">Total Orders</span>
                            </div>
                            <div class="stat-card">
                                <span class="stat-number" id="wishlist-items">0</span>
                                <span class="stat-label">Wishlist Items</span>
                            </div>
                        </div>
                    </div>

                    <div class="profile-card">
                        <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
                        <div class="quick-actions">
                            <a href="products.html" class="action-btn">
                                <i class="fas fa-shopping-bag"></i>
                                Browse Products
                            </a>
                            <a href="cart.html" class="action-btn">
                                <i class="fas fa-shopping-cart"></i>
                                View Cart
                            </a>
                            <button class="action-btn" onclick="clearData()">
                                <i class="fas fa-trash"></i>
                                Clear All Data
                            </button>
                            <button class="action-btn" onclick="exportData()">
                                <i class="fas fa-download"></i>
                                Export Data
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Main Content -->
                <div class="profile-main">
                    <div class="tabs">
                        <div class="tab-list">
                            <button class="tab-button active" data-tab="overview">Overview</button>
                            <button class="tab-button" data-tab="orders">Orders</button>
                            <button class="tab-button" data-tab="wishlist">Wishlist</button>
                            <button class="tab-button" data-tab="settings">Settings</button>
                        </div>

                        <!-- Overview Tab -->
                        <div class="tab-content active" id="overview">
                            <h3>Account Overview</h3>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-label">Account Type</span>
                                    <span class="info-value">Guest User</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Member Since</span>
                                    <span class="info-value" id="member-since">Today</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Cart Items</span>
                                    <span class="info-value" id="cart-items-count">0</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Theme</span>
                                    <span class="info-value" id="current-theme">Auto</span>
                                </div>
                            </div>
                        </div>

                        <!-- Orders Tab -->
                        <div class="tab-content" id="orders">
                            <h3>Order History</h3>
                            <div class="empty-state">
                                <div class="empty-state-icon">
                                    <i class="fas fa-receipt"></i>
                                </div>
                                <h4 class="empty-state-title">No orders yet</h4>
                                <p class="empty-state-description">
                                    You haven't placed any orders yet. Start shopping to see your order history here.
                                </p>
                                <a href="products.html" class="btn btn-primary">
                                    Start Shopping
                                </a>
                            </div>
                        </div>

                        <!-- Wishlist Tab -->
                        <div class="tab-content" id="wishlist">
                            <h3>My Wishlist</h3>
                            <div id="wishlist-content">
                                <div class="empty-state">
                                    <div class="empty-state-icon">
                                        <i class="fas fa-heart"></i>
                                    </div>
                                    <h4 class="empty-state-title">Your wishlist is empty</h4>
                                    <p class="empty-state-description">
                                        Save items you love for later by clicking the heart icon on products.
                                    </p>
                                    <a href="products.html" class="btn btn-primary">
                                        Browse Products
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Settings Tab -->
                        <div class="tab-content" id="settings">
                            <h3>Settings</h3>
                            <div class="form-group">
                                <label class="form-label">Theme Preference</label>
                                <select class="form-select" id="theme-select">
                                    <option value="auto">Auto (System)</option>
                                    <option value="light">Light</option>
                                    <option value="dark">Dark</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Notifications</label>
                                <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                                    <input type="checkbox" id="notifications" checked>
                                    <label for="notifications">Enable notifications</label>
                                </div>
                            </div>
                            <button class="btn btn-primary" onclick="saveSettings()">
                                Save Settings
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>ShopEase</h3>
                    <p>Your trusted online shopping destination for quality products at amazing prices.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="cart.html">Cart</a></li>
                        <li><a href="profile.html">Profile</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 ShopEase. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="back-to-top" aria-label="Back to top">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toast-container"></div>

    <!-- JavaScript Files -->
    <script src="../js/utils.js"></script>
    <script src="../js/main.js"></script>
    <script src="../js/products.js"></script>
    <script src="../js/cart.js"></script>
    
    <!-- Profile Page Script -->
    <script>
        // Profile page functionality
        document.addEventListener('DOMContentLoaded', () => {
            initProfilePage();
        });

        function initProfilePage() {
            // Initialize tabs
            initTabs();
            
            // Load profile data
            loadProfileData();
            
            // Update stats
            updateStats();
        }

        function initTabs() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.getAttribute('data-tab');
                    
                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));
                    
                    // Add active class to clicked button and corresponding content
                    button.classList.add('active');
                    document.getElementById(tabId).classList.add('active');
                });
            });
        }

        function loadProfileData() {
            // Load theme setting
            const themeSelect = document.getElementById('theme-select');
            const currentTheme = localStorage.getItem('theme') || 'auto';
            themeSelect.value = currentTheme;
            document.getElementById('current-theme').textContent = Utils.capitalize(currentTheme);

            // Set member since date
            const memberSince = localStorage.getItem('member_since') || new Date().toISOString();
            document.getElementById('member-since').textContent = Utils.formatDate(memberSince);
        }

        function updateStats() {
            // Update cart items count
            if (window.cartManager) {
                document.getElementById('cart-items-count').textContent = window.cartManager.getItemCount();
            }

            // Update wishlist count
            const wishlist = JSON.parse(localStorage.getItem('wishlist') || '[]');
            document.getElementById('wishlist-items').textContent = wishlist.length;

            // Update total orders (placeholder)
            document.getElementById('total-orders').textContent = '0';
        }

        function saveSettings() {
            const themeSelect = document.getElementById('theme-select');
            const newTheme = themeSelect.value;
            
            if (window.shopEaseApp) {
                window.shopEaseApp.applyTheme(newTheme);
                window.shopEaseApp.showToast('Settings saved successfully!', 'success');
            }
            
            document.getElementById('current-theme').textContent = Utils.capitalize(newTheme);
        }

        function clearData() {
            if (confirm('Are you sure you want to clear all data? This will remove your cart, wishlist, and preferences.')) {
                localStorage.clear();
                if (window.shopEaseApp) {
                    window.shopEaseApp.showToast('All data cleared', 'info');
                    setTimeout(() => location.reload(), 1000);
                }
            }
        }

        function exportData() {
            const data = {
                cart: JSON.parse(localStorage.getItem('shopease_cart') || '[]'),
                wishlist: JSON.parse(localStorage.getItem('wishlist') || '[]'),
                theme: localStorage.getItem('theme') || 'auto',
                memberSince: localStorage.getItem('member_since') || new Date().toISOString(),
                exportedAt: new Date().toISOString()
            };

            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            
            Utils.downloadFile(url, `shopease-profile-${new Date().toISOString().split('T')[0]}.json`);
            
            URL.revokeObjectURL(url);
            
            if (window.shopEaseApp) {
                window.shopEaseApp.showToast('Profile data exported!', 'success');
            }
        }
    </script>
</body>
</html>
