// Products Management - ShopEase E-commerce Application

/**
 * Product Manager Class
 */
class ProductManager {
    constructor() {
        this.products = [];
        this.filteredProducts = [];
        this.currentPage = 1;
        this.productsPerPage = 12;
        this.currentFilters = {
            category: '',
            search: '',
            minPrice: 0,
            maxPrice: 1000,
            sortBy: 'name'
        };
        this.init();
    }

    /**
     * Initialize product manager
     */
    async init() {
        try {
            await this.loadProducts();
            this.initializeFilters();
            this.initializePagination();
            this.bindEvents();
        } catch (error) {
            console.error('Error initializing ProductManager:', error);
        }
    }

    /**
     * Load products data
     */
    async loadProducts() {
        try {
            // In a real application, this would fetch from an API
            this.products = this.generateSampleProducts();
            this.filteredProducts = [...this.products];
            
            // Load featured products on home page
            if (window.location.pathname.includes('index.html') || window.location.pathname === '/') {
                this.loadFeaturedProducts();
            } else {
                this.renderProducts();
            }
        } catch (error) {
            console.error('Error loading products:', error);
            this.showError('Failed to load products');
        }
    }

    /**
     * Generate sample products for demo
     */
    generateSampleProducts() {
        // Start with realistic featured products
        const featuredProducts = [
            {
                id: 'sony-wh1000xm4',
                name: "Sony WH-1000XM4 Wireless Headphones",
                price: 279.99,
                originalPrice: 349.99,
                image: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=300&h=300&fit=crop&crop=center",
                category: "Electronics",
                description: "Industry-leading noise canceling with Dual Noise Sensor technology. Up to 30-hour battery life with quick charge.",
                rating: 4.8,
                reviews: 2847,
                inStock: true,
                featured: true,
                badge: 'sale',
                tags: ["wireless", "noise-canceling", "premium", "sony"]
            },
            {
                id: 'apple-watch-s8',
                name: "Apple Watch Series 8 GPS",
                price: 399.99,
                originalPrice: 429.99,
                image: "https://images.unsplash.com/photo-**********-ef5deaed4a26?w=300&h=300&fit=crop&crop=center",
                category: "Electronics",
                description: "Advanced health monitoring, fitness tracking, and safety features. Always-On Retina display.",
                rating: 4.7,
                reviews: 1923,
                inStock: true,
                featured: true,
                badge: 'sale',
                tags: ["smartwatch", "fitness", "health", "apple"]
            },
            {
                id: 'manduka-yoga-mat',
                name: "Manduka PRO Yoga Mat",
                price: 88.00,
                originalPrice: 120.00,
                image: "https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=300&h=300&fit=crop&crop=center",
                category: "Sports",
                description: "Superior catch grip for traction, high-density cushioning for support and stability. Lifetime guarantee.",
                rating: 4.8,
                reviews: 1456,
                inStock: true,
                featured: true,
                badge: 'sale',
                tags: ["yoga", "premium", "non-slip", "manduka"]
            },
            {
                id: 'nike-air-max-270',
                name: "Nike Air Max 270 Sneakers",
                price: 150.00,
                originalPrice: 180.00,
                image: "https://images.unsplash.com/photo-**********-b41d501d3772?w=300&h=300&fit=crop&crop=center",
                category: "Clothing",
                description: "Nike's biggest heel Air unit yet delivers exceptional comfort. Engineered mesh upper for breathability.",
                rating: 4.5,
                reviews: 2103,
                inStock: true,
                featured: true,
                badge: 'sale',
                tags: ["sneakers", "air-max", "comfortable", "nike"]
            },
            {
                id: 'instant-pot-duo',
                name: "Instant Pot Duo 7-in-1",
                price: 79.95,
                originalPrice: 99.95,
                image: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=300&fit=crop&crop=center",
                category: "Home & Garden",
                description: "7 appliances in 1: pressure cooker, slow cooker, rice cooker, steamer, sauté pan, yogurt maker & warmer.",
                rating: 4.7,
                reviews: 8934,
                inStock: true,
                featured: true,
                badge: 'sale',
                tags: ["pressure-cooker", "multi-cooker", "instant-pot", "kitchen"]
            }
        ];

        // Generate additional products
        const categories = ['Electronics', 'Clothing', 'Home & Garden', 'Sports', 'Books', 'Beauty'];
        const additionalProducts = [];

        for (let i = 1; i <= 45; i++) {
            const category = Utils.randomFromArray(categories);
            const price = Utils.randomBetween(15, 400);
            const originalPrice = Math.random() > 0.6 ? price + Utils.randomBetween(10, 80) : null;
            const rating = (Math.random() * 2 + 3).toFixed(1);
            const reviews = Utils.randomBetween(10, 1000);

            additionalProducts.push({
                id: Utils.generateId(),
                name: this.generateProductName(category, i),
                description: this.generateProductDescription(category),
                category: category,
                price: price,
                originalPrice: originalPrice,
                image: this.getRealisticImage(category),
                rating: parseFloat(rating),
                reviews: reviews,
                inStock: Math.random() > 0.05, // 95% in stock
                featured: Math.random() > 0.85, // 15% featured
                badge: this.getProductBadge(originalPrice, i <= 8, Math.random() > 0.85),
                tags: this.generateProductTags(category)
            });
        }

        return [...featuredProducts, ...additionalProducts];
    }

    /**
     * Generate realistic product name
     */
    generateProductName(category, index) {
        const productNames = {
            'Electronics': [
                'Wireless Bluetooth Earbuds', 'Smart Home Speaker', 'Portable Power Bank',
                'USB-C Fast Charger', 'Bluetooth Keyboard', 'Wireless Mouse', 'Phone Stand',
                'Tablet Case', 'Screen Protector', 'Car Phone Mount'
            ],
            'Clothing': [
                'Cotton Crew Neck T-Shirt', 'Denim Slim Jeans', 'Hooded Sweatshirt',
                'Athletic Running Shorts', 'Casual Button Shirt', 'Knit Sweater',
                'Leather Belt', 'Canvas Sneakers', 'Winter Jacket', 'Summer Dress'
            ],
            'Home & Garden': [
                'Ceramic Coffee Mug', 'Stainless Steel Cookware', 'LED Desk Lamp',
                'Throw Pillow Cover', 'Plant Pot Set', 'Kitchen Utensil Set',
                'Storage Basket', 'Wall Clock', 'Picture Frame', 'Candle Set'
            ],
            'Sports': [
                'Resistance Bands Set', 'Foam Roller', 'Water Bottle', 'Gym Towel',
                'Exercise Mat', 'Dumbbells', 'Jump Rope', 'Fitness Tracker',
                'Running Shoes', 'Sports Backpack'
            ],
            'Books': [
                'Self-Help Guide', 'Mystery Novel', 'Cookbook Collection', 'Travel Guide',
                'Biography', 'Science Fiction', 'History Book', 'Art Book',
                'Children\'s Story', 'Technical Manual'
            ],
            'Beauty': [
                'Moisturizing Face Cream', 'Vitamin C Serum', 'Makeup Brush Set',
                'Lip Balm', 'Shampoo & Conditioner', 'Nail Polish', 'Face Mask',
                'Perfume', 'Hair Styling Tool', 'Skincare Kit'
            ]
        };

        const names = productNames[category] || ['Generic Product'];
        return names[index % names.length] || `${category} Product ${index}`;
    }

    /**
     * Generate realistic product description
     */
    generateProductDescription(category) {
        const descriptions = {
            'Electronics': [
                'High-quality electronic device with advanced features and reliable performance.',
                'Cutting-edge technology designed for modern lifestyle and convenience.',
                'Premium electronic accessory with sleek design and superior functionality.'
            ],
            'Clothing': [
                'Comfortable and stylish apparel made from high-quality materials.',
                'Fashion-forward clothing designed for everyday wear and special occasions.',
                'Durable and trendy garment that combines style with practicality.'
            ],
            'Home & Garden': [
                'Essential home item that adds functionality and style to your living space.',
                'Quality household product designed to make your daily life easier.',
                'Decorative and practical item perfect for modern home decor.'
            ],
            'Sports': [
                'Professional-grade sports equipment for fitness enthusiasts and athletes.',
                'Durable fitness accessory designed to enhance your workout experience.',
                'High-performance sports gear for active lifestyle and training.'
            ],
            'Books': [
                'Engaging and informative book that provides valuable insights and entertainment.',
                'Well-written publication perfect for learning and personal development.',
                'Captivating read that offers knowledge and inspiration.'
            ],
            'Beauty': [
                'Premium beauty product formulated with natural ingredients for best results.',
                'Professional-quality cosmetic item for daily skincare and beauty routine.',
                'Luxurious beauty essential that enhances your natural radiance.'
            ]
        };

        const categoryDescriptions = descriptions[category] || ['Quality product with excellent features.'];
        return Utils.randomFromArray(categoryDescriptions);
    }

    /**
     * Get realistic image for category
     */
    getRealisticImage(category) {
        const imageUrls = {
            'Electronics': [
                'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=300&h=300&fit=crop',
                'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=300&h=300&fit=crop',
                'https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=300&h=300&fit=crop'
            ],
            'Clothing': [
                'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300&h=300&fit=crop',
                'https://images.unsplash.com/photo-1542272604-787c3835535d?w=300&h=300&fit=crop',
                'https://images.unsplash.com/photo-1503341504253-dff4815485f1?w=300&h=300&fit=crop'
            ],
            'Home & Garden': [
                'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=300&fit=crop',
                'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=300&fit=crop',
                'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=300&fit=crop'
            ],
            'Sports': [
                'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=300&fit=crop',
                'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=300&h=300&fit=crop',
                'https://images.unsplash.com/photo-**********-b41d501d3772?w=300&h=300&fit=crop'
            ],
            'Books': [
                'https://images.unsplash.com/photo-1481277542470-605612bd2d61?w=300&h=300&fit=crop',
                'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop',
                'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=300&h=300&fit=crop'
            ],
            'Beauty': [
                'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=300&fit=crop',
                'https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?w=300&h=300&fit=crop',
                'https://images.unsplash.com/photo-1571781926291-c477ebfd024b?w=300&h=300&fit=crop'
            ]
        };

        const categoryImages = imageUrls[category] || ['https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=300&fit=crop'];
        return Utils.randomFromArray(categoryImages);
    }

    /**
     * Get color for category
     */
    getColorForCategory(category) {
        const colors = {
            'Electronics': '3498db',
            'Clothing': 'e74c3c',
            'Home & Garden': '27ae60',
            'Sports': 'f39c12',
            'Books': '9b59b6',
            'Beauty': 'e91e63'
        };
        return colors[category] || '95a5a6';
    }

    /**
     * Get product badge
     */
    getProductBadge(originalPrice, isNew, isFeatured) {
        if (originalPrice) return 'sale';
        if (isNew) return 'new';
        if (isFeatured) return 'featured';
        return null;
    }

    /**
     * Generate product tags
     */
    generateProductTags(category) {
        const tagMap = {
            'Electronics': ['tech', 'gadget', 'digital'],
            'Clothing': ['fashion', 'style', 'apparel'],
            'Home & Garden': ['home', 'decor', 'garden'],
            'Sports': ['fitness', 'outdoor', 'active'],
            'Books': ['reading', 'education', 'literature'],
            'Beauty': ['cosmetics', 'skincare', 'wellness']
        };
        return tagMap[category] || [];
    }

    /**
     * Load featured products for home page
     */
    async loadFeaturedProducts() {
        const featuredContainer = document.getElementById('featured-products-grid');
        if (!featuredContainer) return;

        try {
            // Show loading skeletons
            this.showLoadingSkeletons(featuredContainer);

            // Simulate loading delay
            await Utils.wait(1000);

            // Get featured products
            const featuredProducts = this.products
                .filter(product => product.featured)
                .slice(0, 6);

            // Render featured products
            featuredContainer.innerHTML = '';
            featuredProducts.forEach(product => {
                const productCard = this.createProductCard(product);
                featuredContainer.appendChild(productCard);
            });

            // Initialize product card interactions
            this.initProductCardEvents(featuredContainer);

        } catch (error) {
            console.error('Error loading featured products:', error);
            featuredContainer.innerHTML = '<div class="empty-state">Failed to load featured products</div>';
        }
    }

    /**
     * Show loading skeletons
     */
    showLoadingSkeletons(container) {
        container.innerHTML = '';
        for (let i = 0; i < 6; i++) {
            const skeleton = document.createElement('div');
            skeleton.className = 'product-skeleton';
            skeleton.innerHTML = `
                <div class="skeleton-image"></div>
                <div class="skeleton-content">
                    <div class="skeleton-title"></div>
                    <div class="skeleton-price"></div>
                </div>
            `;
            container.appendChild(skeleton);
        }
    }

    /**
     * Create product card element
     */
    createProductCard(product) {
        const card = document.createElement('div');
        card.className = 'product-card';
        card.setAttribute('data-product-id', product.id);

        const badgeHtml = product.badge ? `<div class="product-badge ${product.badge}">${Utils.capitalize(product.badge)}</div>` : '';
        const originalPriceHtml = product.originalPrice ? `<span class="price-original">${Utils.formatCurrency(product.originalPrice)}</span>` : '';
        const starsHtml = this.generateStarsHtml(product.rating);

        card.innerHTML = `
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}" loading="lazy">
                ${badgeHtml}
            </div>
            <div class="product-info">
                <div class="product-category">${product.category}</div>
                <h3 class="product-title">${product.name}</h3>
                <p class="product-description">${product.description}</p>
                <div class="product-price">
                    <span class="price-current">${Utils.formatCurrency(product.price)}</span>
                    ${originalPriceHtml}
                </div>
                <div class="product-rating">
                    <div class="stars">${starsHtml}</div>
                    <span class="rating-text">(${product.rating}) ${product.reviews} reviews</span>
                </div>
                <div class="product-actions">
                    <button class="btn-add-cart" data-product-id="${product.id}" ${!product.inStock ? 'disabled' : ''}>
                        <i class="fas fa-shopping-cart"></i>
                        ${product.inStock ? 'Add to Cart' : 'Out of Stock'}
                    </button>
                    <button class="btn-wishlist" data-product-id="${product.id}">
                        <i class="far fa-heart"></i>
                    </button>
                </div>
            </div>
        `;

        return card;
    }

    /**
     * Generate stars HTML for rating
     */
    generateStarsHtml(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        let starsHtml = '';
        
        // Full stars
        for (let i = 0; i < fullStars; i++) {
            starsHtml += '<i class="fas fa-star"></i>';
        }
        
        // Half star
        if (hasHalfStar) {
            starsHtml += '<i class="fas fa-star-half-alt"></i>';
        }
        
        // Empty stars
        for (let i = 0; i < emptyStars; i++) {
            starsHtml += '<i class="far fa-star"></i>';
        }

        return starsHtml;
    }

    /**
     * Initialize product card events
     */
    initProductCardEvents(container) {
        // Add to cart buttons
        const addToCartBtns = container.querySelectorAll('.btn-add-cart');
        addToCartBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const productId = btn.getAttribute('data-product-id');
                this.addToCart(productId);
            });
        });

        // Wishlist buttons
        const wishlistBtns = container.querySelectorAll('.btn-wishlist');
        wishlistBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const productId = btn.getAttribute('data-product-id');
                this.toggleWishlist(productId, btn);
            });
        });

        // Product card click (for product details)
        const productCards = container.querySelectorAll('.product-card');
        productCards.forEach(card => {
            card.addEventListener('click', (e) => {
                // Don't trigger if clicking on buttons
                if (e.target.closest('.product-actions')) return;
                
                const productId = card.getAttribute('data-product-id');
                this.showProductDetails(productId);
            });
        });
    }

    /**
     * Add product to cart
     */
    addToCart(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        if (window.CartManager) {
            const cartManager = new CartManager();
            cartManager.addItem(product);
            
            // Update cart count in navigation
            if (window.shopEaseApp) {
                window.shopEaseApp.updateCartCount();
                window.shopEaseApp.showToast(`${product.name} added to cart!`, 'success');
            }
        }
    }

    /**
     * Toggle wishlist
     */
    toggleWishlist(productId, button) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        // Get current wishlist using StorageManager
        const wishlist = StorageManager.get('wishlist', []);
        const isInWishlist = wishlist.includes(productId);

        if (isInWishlist) {
            // Remove from wishlist
            const index = wishlist.indexOf(productId);
            wishlist.splice(index, 1);
            button.classList.remove('active');
            button.querySelector('i').className = 'far fa-heart';

            if (window.shopEaseApp) {
                window.shopEaseApp.showToast(`${product.name} removed from wishlist`, 'info');
            }
        } else {
            // Add to wishlist
            wishlist.push(productId);
            button.classList.add('active');
            button.querySelector('i').className = 'fas fa-heart';

            if (window.shopEaseApp) {
                window.shopEaseApp.showToast(`${product.name} added to wishlist!`, 'success');
            }
        }

        // Save wishlist using StorageManager
        StorageManager.set('wishlist', wishlist);

        // Track wishlist event
        this.trackWishlistEvent(isInWishlist ? 'remove' : 'add', product);
    }

    /**
     * Track wishlist events
     * @param {string} action - Action performed (add/remove)
     * @param {object} product - Product object
     */
    trackWishlistEvent(action, product) {
        const events = StorageManager.get('wishlist_events', []);
        events.push({
            action: action,
            productId: product.id,
            productName: product.name,
            timestamp: new Date().toISOString()
        });

        // Keep only last 100 events
        if (events.length > 100) {
            events.splice(0, events.length - 100);
        }

        StorageManager.set('wishlist_events', events);
    }

    /**
     * Show product details (placeholder for modal or detail page)
     */
    showProductDetails(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        // For now, just show an alert (in a real app, this would open a modal or navigate to detail page)
        if (window.shopEaseApp) {
            window.shopEaseApp.showToast(`Viewing details for ${product.name}`, 'info');
        }
    }

    /**
     * Initialize filters (for products page)
     */
    initializeFilters() {
        // This would be implemented for the products page
        console.log('Filters initialized');
    }

    /**
     * Initialize pagination (for products page)
     */
    initializePagination() {
        // This would be implemented for the products page
        console.log('Pagination initialized');
    }

    /**
     * Bind events (for products page)
     */
    bindEvents() {
        // This would be implemented for the products page
        console.log('Events bound');
    }

    /**
     * Render products (for products page)
     */
    renderProducts() {
        // This would be implemented for the products page
        console.log('Products rendered');
    }

    /**
     * Show error message
     */
    showError(message) {
        if (window.shopEaseApp) {
            window.shopEaseApp.showToast(message, 'error');
        }
    }

    /**
     * Get product by ID
     */
    getProductById(id) {
        return this.products.find(product => product.id === id);
    }

    /**
     * Search products
     */
    searchProducts(query) {
        if (!query) return this.products;
        
        const searchTerm = query.toLowerCase();
        return this.products.filter(product => 
            product.name.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm) ||
            product.category.toLowerCase().includes(searchTerm) ||
            product.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
    }

    /**
     * Filter products by category
     */
    filterByCategory(category) {
        if (!category) return this.products;
        return this.products.filter(product => product.category === category);
    }

    /**
     * Get all categories
     */
    getCategories() {
        const categories = [...new Set(this.products.map(product => product.category))];
        return categories.sort();
    }

    /**
     * Get featured products
     */
    getFeaturedProducts() {
        return this.products.filter(product => product.featured);
    }
}

// Initialize ProductManager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.ProductManager = ProductManager;
    
    // Auto-initialize on products page or if featured products container exists
    if (window.location.pathname.includes('products.html') || document.getElementById('featured-products-grid')) {
        window.productManager = new ProductManager();
    }
});
