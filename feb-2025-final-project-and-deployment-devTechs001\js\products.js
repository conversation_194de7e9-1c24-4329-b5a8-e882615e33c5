// Products Management - ShopEase E-commerce Application

/**
 * Product Manager Class
 */
class ProductManager {
    constructor() {
        this.products = [];
        this.filteredProducts = [];
        this.currentPage = 1;
        this.productsPerPage = 12;
        this.currentFilters = {
            category: '',
            search: '',
            minPrice: 0,
            maxPrice: 1000,
            sortBy: 'name'
        };
        this.init();
    }

    /**
     * Initialize product manager
     */
    async init() {
        try {
            await this.loadProducts();
            this.initializeFilters();
            this.initializePagination();
            this.bindEvents();
        } catch (error) {
            console.error('Error initializing ProductManager:', error);
        }
    }

    /**
     * Load products data
     */
    async loadProducts() {
        try {
            // In a real application, this would fetch from an API
            this.products = this.generateSampleProducts();
            this.filteredProducts = [...this.products];
            
            // Load featured products on home page
            if (window.location.pathname.includes('index.html') || window.location.pathname === '/') {
                this.loadFeaturedProducts();
            } else {
                this.renderProducts();
            }
        } catch (error) {
            console.error('Error loading products:', error);
            this.showError('Failed to load products');
        }
    }

    /**
     * Generate sample products for demo
     */
    generateSampleProducts() {
        const categories = ['Electronics', 'Clothing', 'Home & Garden', 'Sports', 'Books', 'Beauty'];
        const products = [];

        for (let i = 1; i <= 50; i++) {
            const category = Utils.randomFromArray(categories);
            const price = Utils.randomBetween(10, 500);
            const originalPrice = Math.random() > 0.7 ? price + Utils.randomBetween(10, 100) : null;
            const rating = (Math.random() * 2 + 3).toFixed(1); // 3.0 to 5.0
            const reviews = Utils.randomBetween(5, 500);

            products.push({
                id: Utils.generateId(),
                name: `Product ${i}`,
                description: `High-quality ${category.toLowerCase()} product with excellent features and great value for money.`,
                category: category,
                price: price,
                originalPrice: originalPrice,
                image: `https://via.placeholder.com/300x250/${this.getColorForCategory(category)}/ffffff?text=${encodeURIComponent(category)}`,
                rating: parseFloat(rating),
                reviews: reviews,
                inStock: Math.random() > 0.1, // 90% in stock
                featured: Math.random() > 0.8, // 20% featured
                badge: this.getProductBadge(originalPrice, i <= 10, Math.random() > 0.8),
                tags: this.generateProductTags(category)
            });
        }

        return products;
    }

    /**
     * Get color for category
     */
    getColorForCategory(category) {
        const colors = {
            'Electronics': '3498db',
            'Clothing': 'e74c3c',
            'Home & Garden': '27ae60',
            'Sports': 'f39c12',
            'Books': '9b59b6',
            'Beauty': 'e91e63'
        };
        return colors[category] || '95a5a6';
    }

    /**
     * Get product badge
     */
    getProductBadge(originalPrice, isNew, isFeatured) {
        if (originalPrice) return 'sale';
        if (isNew) return 'new';
        if (isFeatured) return 'featured';
        return null;
    }

    /**
     * Generate product tags
     */
    generateProductTags(category) {
        const tagMap = {
            'Electronics': ['tech', 'gadget', 'digital'],
            'Clothing': ['fashion', 'style', 'apparel'],
            'Home & Garden': ['home', 'decor', 'garden'],
            'Sports': ['fitness', 'outdoor', 'active'],
            'Books': ['reading', 'education', 'literature'],
            'Beauty': ['cosmetics', 'skincare', 'wellness']
        };
        return tagMap[category] || [];
    }

    /**
     * Load featured products for home page
     */
    async loadFeaturedProducts() {
        const featuredContainer = document.getElementById('featured-products-grid');
        if (!featuredContainer) return;

        try {
            // Show loading skeletons
            this.showLoadingSkeletons(featuredContainer);

            // Simulate loading delay
            await Utils.wait(1000);

            // Get featured products
            const featuredProducts = this.products
                .filter(product => product.featured)
                .slice(0, 6);

            // Render featured products
            featuredContainer.innerHTML = '';
            featuredProducts.forEach(product => {
                const productCard = this.createProductCard(product);
                featuredContainer.appendChild(productCard);
            });

            // Initialize product card interactions
            this.initProductCardEvents(featuredContainer);

        } catch (error) {
            console.error('Error loading featured products:', error);
            featuredContainer.innerHTML = '<div class="empty-state">Failed to load featured products</div>';
        }
    }

    /**
     * Show loading skeletons
     */
    showLoadingSkeletons(container) {
        container.innerHTML = '';
        for (let i = 0; i < 6; i++) {
            const skeleton = document.createElement('div');
            skeleton.className = 'product-skeleton';
            skeleton.innerHTML = `
                <div class="skeleton-image"></div>
                <div class="skeleton-content">
                    <div class="skeleton-title"></div>
                    <div class="skeleton-price"></div>
                </div>
            `;
            container.appendChild(skeleton);
        }
    }

    /**
     * Create product card element
     */
    createProductCard(product) {
        const card = document.createElement('div');
        card.className = 'product-card';
        card.setAttribute('data-product-id', product.id);

        const badgeHtml = product.badge ? `<div class="product-badge ${product.badge}">${Utils.capitalize(product.badge)}</div>` : '';
        const originalPriceHtml = product.originalPrice ? `<span class="price-original">${Utils.formatCurrency(product.originalPrice)}</span>` : '';
        const starsHtml = this.generateStarsHtml(product.rating);

        card.innerHTML = `
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}" loading="lazy">
                ${badgeHtml}
            </div>
            <div class="product-info">
                <div class="product-category">${product.category}</div>
                <h3 class="product-title">${product.name}</h3>
                <p class="product-description">${product.description}</p>
                <div class="product-price">
                    <span class="price-current">${Utils.formatCurrency(product.price)}</span>
                    ${originalPriceHtml}
                </div>
                <div class="product-rating">
                    <div class="stars">${starsHtml}</div>
                    <span class="rating-text">(${product.rating}) ${product.reviews} reviews</span>
                </div>
                <div class="product-actions">
                    <button class="btn-add-cart" data-product-id="${product.id}" ${!product.inStock ? 'disabled' : ''}>
                        <i class="fas fa-shopping-cart"></i>
                        ${product.inStock ? 'Add to Cart' : 'Out of Stock'}
                    </button>
                    <button class="btn-wishlist" data-product-id="${product.id}">
                        <i class="far fa-heart"></i>
                    </button>
                </div>
            </div>
        `;

        return card;
    }

    /**
     * Generate stars HTML for rating
     */
    generateStarsHtml(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        let starsHtml = '';
        
        // Full stars
        for (let i = 0; i < fullStars; i++) {
            starsHtml += '<i class="fas fa-star"></i>';
        }
        
        // Half star
        if (hasHalfStar) {
            starsHtml += '<i class="fas fa-star-half-alt"></i>';
        }
        
        // Empty stars
        for (let i = 0; i < emptyStars; i++) {
            starsHtml += '<i class="far fa-star"></i>';
        }

        return starsHtml;
    }

    /**
     * Initialize product card events
     */
    initProductCardEvents(container) {
        // Add to cart buttons
        const addToCartBtns = container.querySelectorAll('.btn-add-cart');
        addToCartBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const productId = btn.getAttribute('data-product-id');
                this.addToCart(productId);
            });
        });

        // Wishlist buttons
        const wishlistBtns = container.querySelectorAll('.btn-wishlist');
        wishlistBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const productId = btn.getAttribute('data-product-id');
                this.toggleWishlist(productId, btn);
            });
        });

        // Product card click (for product details)
        const productCards = container.querySelectorAll('.product-card');
        productCards.forEach(card => {
            card.addEventListener('click', (e) => {
                // Don't trigger if clicking on buttons
                if (e.target.closest('.product-actions')) return;
                
                const productId = card.getAttribute('data-product-id');
                this.showProductDetails(productId);
            });
        });
    }

    /**
     * Add product to cart
     */
    addToCart(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        if (window.CartManager) {
            const cartManager = new CartManager();
            cartManager.addItem(product);
            
            // Update cart count in navigation
            if (window.shopEaseApp) {
                window.shopEaseApp.updateCartCount();
                window.shopEaseApp.showToast(`${product.name} added to cart!`, 'success');
            }
        }
    }

    /**
     * Toggle wishlist
     */
    toggleWishlist(productId, button) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        // Get current wishlist from localStorage
        const wishlist = JSON.parse(localStorage.getItem('wishlist') || '[]');
        const isInWishlist = wishlist.includes(productId);

        if (isInWishlist) {
            // Remove from wishlist
            const index = wishlist.indexOf(productId);
            wishlist.splice(index, 1);
            button.classList.remove('active');
            button.querySelector('i').className = 'far fa-heart';
            
            if (window.shopEaseApp) {
                window.shopEaseApp.showToast(`${product.name} removed from wishlist`, 'info');
            }
        } else {
            // Add to wishlist
            wishlist.push(productId);
            button.classList.add('active');
            button.querySelector('i').className = 'fas fa-heart';
            
            if (window.shopEaseApp) {
                window.shopEaseApp.showToast(`${product.name} added to wishlist!`, 'success');
            }
        }

        localStorage.setItem('wishlist', JSON.stringify(wishlist));
    }

    /**
     * Show product details (placeholder for modal or detail page)
     */
    showProductDetails(productId) {
        const product = this.products.find(p => p.id === productId);
        if (!product) return;

        // For now, just show an alert (in a real app, this would open a modal or navigate to detail page)
        if (window.shopEaseApp) {
            window.shopEaseApp.showToast(`Viewing details for ${product.name}`, 'info');
        }
    }

    /**
     * Initialize filters (for products page)
     */
    initializeFilters() {
        // This would be implemented for the products page
        console.log('Filters initialized');
    }

    /**
     * Initialize pagination (for products page)
     */
    initializePagination() {
        // This would be implemented for the products page
        console.log('Pagination initialized');
    }

    /**
     * Bind events (for products page)
     */
    bindEvents() {
        // This would be implemented for the products page
        console.log('Events bound');
    }

    /**
     * Render products (for products page)
     */
    renderProducts() {
        // This would be implemented for the products page
        console.log('Products rendered');
    }

    /**
     * Show error message
     */
    showError(message) {
        if (window.shopEaseApp) {
            window.shopEaseApp.showToast(message, 'error');
        }
    }

    /**
     * Get product by ID
     */
    getProductById(id) {
        return this.products.find(product => product.id === id);
    }

    /**
     * Search products
     */
    searchProducts(query) {
        if (!query) return this.products;
        
        const searchTerm = query.toLowerCase();
        return this.products.filter(product => 
            product.name.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm) ||
            product.category.toLowerCase().includes(searchTerm) ||
            product.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
    }

    /**
     * Filter products by category
     */
    filterByCategory(category) {
        if (!category) return this.products;
        return this.products.filter(product => product.category === category);
    }

    /**
     * Get all categories
     */
    getCategories() {
        const categories = [...new Set(this.products.map(product => product.category))];
        return categories.sort();
    }

    /**
     * Get featured products
     */
    getFeaturedProducts() {
        return this.products.filter(product => product.featured);
    }
}

// Initialize ProductManager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.ProductManager = ProductManager;
    
    // Auto-initialize on products page or if featured products container exists
    if (window.location.pathname.includes('products.html') || document.getElementById('featured-products-grid')) {
        window.productManager = new ProductManager();
    }
});
