/* Main CSS - ShopEase E-commerce Application */

/* CSS Custom Properties */
:root {
    /* Colors */
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --accent-color: #e74c3c;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    
    /* Neutral Colors */
    --white: #ffffff;
    --light-gray: #f8f9fa;
    --gray: #6c757d;
    --dark-gray: #495057;
    --black: #212529;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-dark: #2c3e50;
    
    /* Text Colors */
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --text-light: #ffffff;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;
    --font-size-4xl: 2.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.1);
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 50%;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-base: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1040;
    --z-popover: 1050;
    --z-tooltip: 1060;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-dark: #121212;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --light-gray: #2d2d2d;
    --gray: #888888;

    /* Dark theme specific colors */
    --shadow-sm: 0 1px 3px rgba(255, 255, 255, 0.1);
    --shadow-md: 0 4px 6px rgba(255, 255, 255, 0.1);
    --shadow-lg: 0 10px 25px rgba(255, 255, 255, 0.1);
    --shadow-xl: 0 20px 40px rgba(255, 255, 255, 0.1);
}

/* High Contrast Theme Variables */
[data-theme="high-contrast"] {
    --primary-color: #0066ff;
    --primary-dark: #0052cc;
    --bg-primary: #ffffff;
    --bg-secondary: #f0f0f0;
    --bg-dark: #000000;
    --text-primary: #000000;
    --text-secondary: #333333;
    --light-gray: #cccccc;
    --gray: #666666;
    --accent-color: #ff0000;
}

/* Theme transition styles */
.theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease !important;
}

.theme-transition * {
    transition: background-color 0.3s ease,
               color 0.3s ease,
               border-color 0.3s ease,
               box-shadow 0.3s ease !important;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: background-color var(--transition-base), color var(--transition-base);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
    cursor: pointer;
}

a:hover {
    color: var(--primary-dark);
    cursor: pointer;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity var(--transition-base);
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--light-gray);
    border-top: 4px solid var(--primary-color);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Header */
.header {
    background: var(--bg-primary);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    transition: background-color var(--transition-base);
}

.navbar {
    padding: var(--spacing-md) 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h1 {
    color: var(--primary-color);
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin: 0;
}

.logo i {
    margin-right: var(--spacing-sm);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
    position: relative;
    z-index: 1;
}

.nav-link {
    color: var(--text-primary);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background: rgba(52, 152, 219, 0.1);
}

.cart-count {
    background: var(--accent-color);
    color: var(--white);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--radius-full);
    margin-left: var(--spacing-xs);
    min-width: 18px;
    text-align: center;
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.nav-actions button {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.nav-actions button:hover {
    color: var(--primary-color);
    background: rgba(52, 152, 219, 0.1);
}

.theme-toggle {
    position: relative;
    overflow: hidden;
}

.theme-toggle i {
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.theme-toggle:hover i {
    transform: scale(1.1);
}

.theme-toggle::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(52, 152, 219, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
    pointer-events: none;
}

.theme-toggle:active::after {
    width: 40px;
    height: 40px;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    z-index: var(--z-fixed);
}

.mobile-menu-toggle:hover {
    color: var(--primary-color);
    background: rgba(52, 152, 219, 0.1);
}

.mobile-menu-toggle:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Mobile menu toggle animation */
.mobile-menu-toggle i {
    transition: transform var(--transition-fast);
}

.mobile-menu-toggle.active i {
    transform: rotate(90deg);
}

/* Search Bar */
.search-bar {
    background: var(--bg-secondary);
    padding: var(--spacing-md) 0;
    border-top: 1px solid var(--light-gray);
    transform: translateY(-100%);
    opacity: 0;
    transition: all var(--transition-base);
}

.search-bar.active {
    transform: translateY(0);
    opacity: 1;
}

.search-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: flex;
    gap: var(--spacing-sm);
}

.search-container input {
    flex: 1;
    padding: var(--spacing-md);
    border: 2px solid var(--light-gray);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: border-color var(--transition-fast);
}

.search-container input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.search-btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.search-btn:hover {
    background: var(--primary-dark);
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: var(--spacing-xxl) 0;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hero-pattern" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23hero-pattern)"/></svg>');
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--white);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-xl);
    color: rgba(255, 255, 255, 0.9);
}

.hero-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-graphic {
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.hero-graphic i {
    font-size: 4rem;
    color: var(--white);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-align: center;
    justify-content: center;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Sections */
.section-title {
    text-align: center;
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.section-subtitle {
    text-align: center;
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xxl);
}

.section-header {
    margin-bottom: var(--spacing-xxl);
}

.section-actions {
    text-align: center;
    margin-top: var(--spacing-xxl);
}

/* Features Section */
.features {
    padding: var(--spacing-xxl) 0;
    background: var(--bg-secondary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
}

.feature-card {
    background: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: var(--radius-full);
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto var(--spacing-lg);
    color: var(--white);
    font-size: var(--font-size-2xl);
}

.feature-card h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.feature-card p {
    color: var(--text-secondary);
    margin: 0;
}

/* Featured Products */
.featured-products {
    padding: var(--spacing-xxl) 0;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

/* Product Skeleton */
.product-skeleton {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-image {
    width: 100%;
    height: 200px;
    background: var(--light-gray);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
}

.skeleton-title {
    height: 20px;
    background: var(--light-gray);
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-sm);
    width: 80%;
}

.skeleton-price {
    height: 16px;
    background: var(--light-gray);
    border-radius: var(--radius-sm);
    width: 40%;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Newsletter */
.newsletter {
    background: var(--bg-dark);
    color: var(--white);
    padding: var(--spacing-xxl) 0;
    text-align: center;
}

.newsletter h2 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
}

.newsletter p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--spacing-xl);
}

.newsletter-form {
    display: flex;
    max-width: 400px;
    margin: 0 auto;
    gap: var(--spacing-sm);
}

.newsletter-form input {
    flex: 1;
    padding: var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
}

.newsletter-form input:focus {
    outline: none;
}

/* Footer */
.footer {
    background: var(--bg-dark);
    color: var(--white);
    padding: var(--spacing-xxl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section h3,
.footer-section h4 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
}

.footer-section p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--spacing-md);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    transition: color var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--white);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--white);
    transition: all var(--transition-fast);
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

.contact-info p {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-sm);
}

/* Back to Top */
.back-to-top {
    position: fixed;
    bottom: var(--spacing-xl);
    right: var(--spacing-xl);
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--radius-full);
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-base);
    z-index: var(--z-fixed);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    z-index: var(--z-tooltip);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.toast {
    background: var(--bg-primary);
    color: var(--text-primary);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--primary-color);
    transform: translateX(100%);
    opacity: 0;
    transition: all var(--transition-base);
    max-width: 300px;
}

.toast.show {
    transform: translateX(0);
    opacity: 1;
}

.toast.success {
    border-left-color: var(--success-color);
}

.toast.error {
    border-left-color: var(--accent-color);
}

.toast.warning {
    border-left-color: var(--warning-color);
}

.toast.info {
    border-left-color: var(--info-color);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-sm); }
.mt-2 { margin-top: var(--spacing-md); }
.mt-3 { margin-top: var(--spacing-lg); }
.mt-4 { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-sm); }
.mb-2 { margin-bottom: var(--spacing-md); }
.mb-3 { margin-bottom: var(--spacing-lg); }
.mb-4 { margin-bottom: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-sm); }
.p-2 { padding: var(--spacing-md); }
.p-3 { padding: var(--spacing-lg); }
.p-4 { padding: var(--spacing-xl); }
