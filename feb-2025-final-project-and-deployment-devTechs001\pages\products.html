<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - ShopEase</title>
    <meta name="description" content="Browse our extensive collection of high-quality products at amazing prices">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <link rel="stylesheet" href="../css/components.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p>Loading Products...</p>
    </div>

    <!-- Header Section -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <h1><i class="fas fa-shopping-bag"></i> ShopEase</h1>
                </div>
                
                <div class="nav-menu" id="nav-menu">
                    <a href="../index.html" class="nav-link">Home</a>
                    <a href="products.html" class="nav-link active">Products</a>
                    <a href="cart.html" class="nav-link">
                        <i class="fas fa-shopping-cart"></i>
                        Cart <span class="cart-count" id="cart-count">0</span>
                    </a>
                    <a href="profile.html" class="nav-link">
                        <i class="fas fa-user"></i>
                        Profile
                    </a>
                </div>
                
                <div class="nav-actions">
                    <button class="search-toggle" id="search-toggle">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="theme-toggle" id="theme-toggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
            
            <!-- Search Bar -->
            <div class="search-bar" id="search-bar">
                <div class="search-container">
                    <input type="text" placeholder="Search products..." id="search-input">
                    <button class="search-btn" id="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Products Page Content -->
    <main class="products-page">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h1>Our Products</h1>
                <p>Discover our amazing collection of high-quality products</p>
            </div>

            <!-- Filters and Search -->
            <div class="products-controls">
                <div class="search-component">
                    <i class="search-icon fas fa-search"></i>
                    <input type="text" class="search-input" id="products-search" placeholder="Search products...">
                </div>
                
                <div class="filter-group">
                    <div class="filter-item">
                        <label class="filter-label">Category</label>
                        <select class="filter-select" id="category-filter">
                            <option value="">All Categories</option>
                        </select>
                    </div>
                    
                    <div class="filter-item">
                        <label class="filter-label">Sort By</label>
                        <select class="filter-select" id="sort-filter">
                            <option value="name">Name (A-Z)</option>
                            <option value="name-desc">Name (Z-A)</option>
                            <option value="price">Price (Low to High)</option>
                            <option value="price-desc">Price (High to Low)</option>
                            <option value="rating">Rating</option>
                            <option value="newest">Newest First</option>
                        </select>
                    </div>
                    
                    <div class="filter-item">
                        <label class="filter-label">Price Range</label>
                        <div class="price-range">
                            <input type="range" id="price-range" min="0" max="1000" value="1000" class="price-slider">
                            <span class="price-display">$0 - $<span id="max-price">1000</span></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="products-section">
                <div class="products-header-info">
                    <span class="products-count" id="products-count">Loading products...</span>
                    <div class="view-toggle">
                        <button class="view-btn active" data-view="grid" title="Grid View">
                            <i class="fas fa-th"></i>
                        </button>
                        <button class="view-btn" data-view="list" title="List View">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
                
                <div class="products-grid" id="products-grid">
                    <!-- Loading skeletons -->
                    <div class="product-skeleton">
                        <div class="skeleton-image"></div>
                        <div class="skeleton-content">
                            <div class="skeleton-title"></div>
                            <div class="skeleton-price"></div>
                        </div>
                    </div>
                    <div class="product-skeleton">
                        <div class="skeleton-image"></div>
                        <div class="skeleton-content">
                            <div class="skeleton-title"></div>
                            <div class="skeleton-price"></div>
                        </div>
                    </div>
                    <div class="product-skeleton">
                        <div class="skeleton-image"></div>
                        <div class="skeleton-content">
                            <div class="skeleton-title"></div>
                            <div class="skeleton-price"></div>
                        </div>
                    </div>
                    <div class="product-skeleton">
                        <div class="skeleton-image"></div>
                        <div class="skeleton-content">
                            <div class="skeleton-title"></div>
                            <div class="skeleton-price"></div>
                        </div>
                    </div>
                    <div class="product-skeleton">
                        <div class="skeleton-image"></div>
                        <div class="skeleton-content">
                            <div class="skeleton-title"></div>
                            <div class="skeleton-price"></div>
                        </div>
                    </div>
                    <div class="product-skeleton">
                        <div class="skeleton-image"></div>
                        <div class="skeleton-content">
                            <div class="skeleton-title"></div>
                            <div class="skeleton-price"></div>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="pagination" id="pagination">
                    <!-- Pagination will be generated by JavaScript -->
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>ShopEase</h3>
                    <p>Your trusted online shopping destination for quality products at amazing prices.</p>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="cart.html">Cart</a></li>
                        <li><a href="profile.html">Profile</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Customer Service</h4>
                    <ul>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">FAQ</a></li>
                        <li><a href="#">Shipping Info</a></li>
                        <li><a href="#">Returns</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-phone"></i> +254 758 175 275</p>
                        <p><i class="fas fa-map-marker-alt"></i> Nairobi, Kenya</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 ShopEase. All rights reserved.</p>
                <div class="footer-links">
                    <a href="#">Privacy Policy</a>
                    <a href="#">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="back-to-top" aria-label="Back to top">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toast-container"></div>

    <!-- Product Detail Modal -->
    <div class="modal" id="product-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Product Details</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="product-modal-body">
                <!-- Product details will be loaded here -->
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="../js/utils.js"></script>
    <script src="../js/main.js"></script>
    <script src="../js/products.js"></script>
    <script src="../js/cart.js"></script>
    
    <!-- Products Page Specific Script -->
    <script>
        // Products page specific functionality
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize products page
            if (window.ProductManager) {
                window.productsPageManager = new ProductsPageManager();
            }
        });
    </script>
</body>
</html>
