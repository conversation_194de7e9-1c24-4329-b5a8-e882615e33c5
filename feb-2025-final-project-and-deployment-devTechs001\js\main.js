// Main JavaScript - ShopEase E-commerce Application

/**
 * Main Application Class
 */
class ShopEaseApp {
    constructor() {
        this.isLoading = true;
        this.theme = StorageManager.get('theme', 'auto');
        this.settings = StorageManager.get('user_settings', {
            theme: 'auto',
            notifications: true,
            animations: true,
            language: 'en',
            currency: 'USD'
        });
        this.init();
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            // Show loading screen
            this.showLoading();

            // Initialize theme
            this.initTheme();

            // Initialize navigation
            this.initNavigation();

            // Initialize search
            this.initSearch();

            // Initialize back to top button
            this.initBackToTop();

            // Initialize newsletter
            this.initNewsletter();

            // Initialize modals
            this.initModals();

            // Initialize tooltips
            this.initTooltips();

            // Initialize animations
            this.initAnimations();

            // Load page-specific content
            await this.loadPageContent();

            // Hide loading screen
            this.hideLoading();

            console.log('ShopEase application initialized successfully');
        } catch (error) {
            console.error('Error initializing application:', error);
            this.showToast('Error loading application', 'error');
        }
    }

    /**
     * Show loading screen
     */
    showLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.remove('hidden');
        }
    }

    /**
     * Hide loading screen
     */
    hideLoading() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            setTimeout(() => {
                loadingScreen.classList.add('hidden');
                this.isLoading = false;
            }, 1000);
        }
    }

    /**
     * Initialize theme system
     */
    initTheme() {
        const themeToggle = document.getElementById('theme-toggle');

        // Apply saved theme
        this.applyTheme(this.settings.theme);

        // Theme toggle event listener
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // Listen for system theme changes
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', (e) => {
                if (this.settings.theme === 'auto') {
                    this.applyTheme('auto');
                    this.showToast(`Theme automatically switched to ${e.matches ? 'dark' : 'light'} mode`, 'info');
                }
            });
        }

        // Initialize theme transition
        this.initThemeTransition();
    }

    /**
     * Apply theme
     * @param {string} theme - Theme to apply (light, dark, auto)
     */
    applyTheme(theme) {
        const html = document.documentElement;
        const themeToggle = document.getElementById('theme-toggle');

        // Add transition class for smooth theme switching
        html.classList.add('theme-transition');

        let actualTheme = theme;
        if (theme === 'auto') {
            const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
            actualTheme = prefersDark ? 'dark' : 'light';
        }

        html.setAttribute('data-theme', actualTheme);

        // Update theme toggle icon with animation
        if (themeToggle) {
            const icon = themeToggle.querySelector('i');
            if (icon) {
                // Add rotation animation
                icon.style.transform = 'rotate(180deg)';

                setTimeout(() => {
                    icon.className = actualTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
                    icon.style.transform = 'rotate(0deg)';
                }, 150);
            }

            // Update tooltip
            const tooltip = this.getThemeTooltip(theme);
            themeToggle.setAttribute('title', tooltip);
            themeToggle.setAttribute('aria-label', tooltip);
        }

        // Update settings
        this.settings.theme = theme;
        this.saveSettings();

        // Remove transition class after animation
        setTimeout(() => {
            html.classList.remove('theme-transition');
        }, 300);

        // Dispatch theme change event
        window.dispatchEvent(new CustomEvent('themeChanged', {
            detail: { theme: theme, actualTheme: actualTheme }
        }));
    }

    /**
     * Toggle theme (cycles through auto -> light -> dark -> auto)
     */
    toggleTheme() {
        const themeOrder = ['auto', 'light', 'dark'];
        const currentIndex = themeOrder.indexOf(this.settings.theme);
        const nextIndex = (currentIndex + 1) % themeOrder.length;
        const newTheme = themeOrder[nextIndex];

        this.applyTheme(newTheme);
        this.showToast(`Theme switched to ${newTheme} mode`, 'info');
    }

    /**
     * Get theme tooltip text
     * @param {string} theme - Current theme
     * @returns {string} Tooltip text
     */
    getThemeTooltip(theme) {
        const tooltips = {
            'auto': 'Theme: Auto (follows system)',
            'light': 'Theme: Light mode',
            'dark': 'Theme: Dark mode'
        };
        return tooltips[theme] || 'Toggle theme';
    }

    /**
     * Initialize theme transition styles
     */
    initThemeTransition() {
        const style = document.createElement('style');
        style.textContent = `
            .theme-transition,
            .theme-transition *,
            .theme-transition *:before,
            .theme-transition *:after {
                transition: background-color 0.3s ease,
                           color 0.3s ease,
                           border-color 0.3s ease,
                           box-shadow 0.3s ease !important;
            }

            .theme-transition .loading-spinner {
                transition: border-color 0.3s ease !important;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Save user settings
     */
    saveSettings() {
        StorageManager.set('user_settings', this.settings);
        StorageManager.set('settings_updated', new Date().toISOString());
    }

    /**
     * Get user settings
     * @returns {object} Current user settings
     */
    getSettings() {
        return { ...this.settings };
    }

    /**
     * Update specific setting
     * @param {string} key - Setting key
     * @param {*} value - Setting value
     */
    updateSetting(key, value) {
        this.settings[key] = value;
        this.saveSettings();

        // Apply theme if theme setting changed
        if (key === 'theme') {
            this.applyTheme(value);
        }

        // Dispatch settings change event
        window.dispatchEvent(new CustomEvent('settingsChanged', {
            detail: { key, value, settings: this.getSettings() }
        }));
    }

    /**
     * Initialize navigation
     */
    initNavigation() {
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const navMenu = document.getElementById('nav-menu');

        // Mobile menu toggle
        if (mobileMenuToggle && navMenu) {
            mobileMenuToggle.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                const isActive = navMenu.classList.contains('active');

                // Toggle menu visibility
                navMenu.classList.toggle('active');
                mobileMenuToggle.classList.toggle('active');

                // Update hamburger icon with animation
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = !isActive ? 'fas fa-times' : 'fas fa-bars';
                }

                // Prevent body scroll when menu is open
                document.body.style.overflow = !isActive ? 'hidden' : '';

                // Add ARIA attributes for accessibility
                mobileMenuToggle.setAttribute('aria-expanded', !isActive);
                navMenu.setAttribute('aria-hidden', isActive);
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!mobileMenuToggle.contains(e.target) && !navMenu.contains(e.target)) {
                    this.closeMobileMenu(mobileMenuToggle, navMenu);
                }
            });

            // Close mobile menu with Escape key
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && navMenu.classList.contains('active')) {
                    this.closeMobileMenu(mobileMenuToggle, navMenu);
                }
            });

            // Close mobile menu when clicking on nav links
            const navLinks = navMenu.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    this.closeMobileMenu(mobileMenuToggle, navMenu);
                });
            });
        }

        // Update cart count
        this.updateCartCount();
    }

    /**
     * Close mobile menu helper
     * @param {HTMLElement} mobileMenuToggle - Mobile menu toggle button
     * @param {HTMLElement} navMenu - Navigation menu
     */
    closeMobileMenu(mobileMenuToggle, navMenu) {
        navMenu.classList.remove('active');
        mobileMenuToggle.classList.remove('active');

        const icon = mobileMenuToggle.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-bars';
        }

        // Restore body scroll
        document.body.style.overflow = '';

        // Update ARIA attributes
        mobileMenuToggle.setAttribute('aria-expanded', 'false');
        navMenu.setAttribute('aria-hidden', 'true');
    }

    /**
     * Initialize search functionality
     */
    initSearch() {
        const searchToggle = document.getElementById('search-toggle');
        const searchBar = document.getElementById('search-bar');
        const searchInput = document.getElementById('search-input');
        const searchBtn = document.getElementById('search-btn');

        // Search toggle
        if (searchToggle && searchBar) {
            searchToggle.addEventListener('click', () => {
                searchBar.classList.toggle('active');
                if (searchBar.classList.contains('active') && searchInput) {
                    setTimeout(() => searchInput.focus(), 300);
                }
            });
        }

        // Search functionality
        if (searchInput && searchBtn) {
            const performSearch = () => {
                const query = searchInput.value.trim();
                if (query) {
                    // Redirect to products page with search query
                    window.location.href = `pages/products.html?search=${encodeURIComponent(query)}`;
                }
            };

            searchBtn.addEventListener('click', performSearch);
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        }
    }

    /**
     * Initialize back to top button
     */
    initBackToTop() {
        const backToTopBtn = document.getElementById('back-to-top');
        
        if (backToTopBtn) {
            // Show/hide button based on scroll position
            const toggleBackToTop = Utils.throttle(() => {
                if (window.pageYOffset > 300) {
                    backToTopBtn.classList.add('visible');
                } else {
                    backToTopBtn.classList.remove('visible');
                }
            }, 100);

            window.addEventListener('scroll', toggleBackToTop);

            // Scroll to top when clicked
            backToTopBtn.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    }

    /**
     * Initialize newsletter subscription
     */
    initNewsletter() {
        const newsletterForm = document.getElementById('newsletter-form');
        
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', (e) => {
                e.preventDefault();
                
                const emailInput = document.getElementById('newsletter-email');
                const email = emailInput.value.trim();
                
                if (!Utils.isValidEmail(email)) {
                    this.showToast('Please enter a valid email address', 'error');
                    return;
                }

                // Simulate newsletter subscription
                this.subscribeToNewsletter(email);
            });
        }
    }

    /**
     * Subscribe to newsletter
     * @param {string} email - Email address
     */
    async subscribeToNewsletter(email) {
        try {
            // Simulate API call
            await Utils.wait(1000);
            
            // Save to localStorage (in real app, this would be an API call)
            const subscribers = JSON.parse(localStorage.getItem('newsletter_subscribers') || '[]');
            if (!subscribers.includes(email)) {
                subscribers.push(email);
                localStorage.setItem('newsletter_subscribers', JSON.stringify(subscribers));
                
                this.showToast('Successfully subscribed to newsletter!', 'success');
                document.getElementById('newsletter-email').value = '';
            } else {
                this.showToast('You are already subscribed!', 'info');
            }
        } catch (error) {
            console.error('Newsletter subscription error:', error);
            this.showToast('Failed to subscribe. Please try again.', 'error');
        }
    }

    /**
     * Initialize modals
     */
    initModals() {
        // Close modal when clicking outside or on close button
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target);
            }
            
            if (e.target.classList.contains('modal-close')) {
                const modal = e.target.closest('.modal');
                if (modal) {
                    this.closeModal(modal);
                }
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const activeModal = document.querySelector('.modal.active');
                if (activeModal) {
                    this.closeModal(activeModal);
                }
            }
        });
    }

    /**
     * Open modal
     * @param {string} modalId - Modal ID
     */
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
    }

    /**
     * Close modal
     * @param {HTMLElement} modal - Modal element
     */
    closeModal(modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
    }

    /**
     * Initialize tooltips
     */
    initTooltips() {
        const tooltips = document.querySelectorAll('[data-tooltip]');
        
        tooltips.forEach(element => {
            const tooltipText = element.getAttribute('data-tooltip');
            
            element.addEventListener('mouseenter', () => {
                this.showTooltip(element, tooltipText);
            });
            
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    }

    /**
     * Show tooltip
     * @param {HTMLElement} element - Target element
     * @param {string} text - Tooltip text
     */
    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip-popup';
        tooltip.textContent = text;
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
        
        setTimeout(() => tooltip.classList.add('visible'), 10);
    }

    /**
     * Hide tooltip
     */
    hideTooltip() {
        const tooltip = document.querySelector('.tooltip-popup');
        if (tooltip) {
            tooltip.remove();
        }
    }

    /**
     * Initialize animations
     */
    initAnimations() {
        // Intersection Observer for scroll animations
        if ('IntersectionObserver' in window) {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, observerOptions);

            // Observe elements with animation classes
            const animatedElements = document.querySelectorAll('.animate-on-scroll');
            animatedElements.forEach(el => observer.observe(el));
        }
    }

    /**
     * Load page-specific content
     */
    async loadPageContent() {
        const currentPage = this.getCurrentPage();
        
        switch (currentPage) {
            case 'home':
                await this.loadHomePage();
                break;
            case 'products':
                await this.loadProductsPage();
                break;
            case 'cart':
                await this.loadCartPage();
                break;
            case 'profile':
                await this.loadProfilePage();
                break;
            default:
                console.log('Unknown page:', currentPage);
        }
    }

    /**
     * Get current page name
     * @returns {string} Current page name
     */
    getCurrentPage() {
        const path = window.location.pathname;
        if (path.includes('products.html')) return 'products';
        if (path.includes('cart.html')) return 'cart';
        if (path.includes('profile.html')) return 'profile';
        return 'home';
    }

    /**
     * Load home page content
     */
    async loadHomePage() {
        // Load featured products
        if (window.ProductManager) {
            const productManager = new ProductManager();
            await productManager.loadFeaturedProducts();
        }
    }

    /**
     * Load products page content
     */
    async loadProductsPage() {
        // This will be handled by products.js
        console.log('Products page loaded');
    }

    /**
     * Load cart page content
     */
    async loadCartPage() {
        // This will be handled by cart.js
        console.log('Cart page loaded');
    }

    /**
     * Load profile page content
     */
    async loadProfilePage() {
        // This will be handled by profile.js
        console.log('Profile page loaded');
    }

    /**
     * Update cart count in navigation
     */
    updateCartCount() {
        const cartCountElement = document.getElementById('cart-count');
        if (cartCountElement && window.CartManager) {
            const cartManager = new CartManager();
            const itemCount = cartManager.getItemCount();
            cartCountElement.textContent = itemCount;
        }
    }

    /**
     * Show toast notification
     * @param {string} message - Message to show
     * @param {string} type - Toast type (success, error, warning, info)
     * @param {number} duration - Duration in milliseconds
     */
    showToast(message, type = 'info', duration = 3000) {
        const toastContainer = document.getElementById('toast-container');
        if (!toastContainer) return;

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${this.getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        toastContainer.appendChild(toast);

        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);

        // Hide and remove toast
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }

    /**
     * Get toast icon based on type
     * @param {string} type - Toast type
     * @returns {string} Icon class
     */
    getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.shopEaseApp = new ShopEaseApp();
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible' && window.shopEaseApp) {
        // Refresh cart count when page becomes visible
        window.shopEaseApp.updateCartCount();
    }
});

// Handle window resize
window.addEventListener('resize', Utils.throttle(() => {
    if (window.shopEaseApp) {
        // Update mobile menu state on resize
        const navMenu = document.getElementById('nav-menu');
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');

        if (navMenu && mobileMenuToggle && !Utils.isMobile()) {
            window.shopEaseApp.closeMobileMenu(mobileMenuToggle, navMenu);
        }
    }
}, 250));
