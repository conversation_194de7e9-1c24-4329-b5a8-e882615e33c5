/* Responsive Design Styles */

/* Base Mobile-First Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    color: #2c3e50;
}

/* Container Responsive Behavior */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Navigation Responsive */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.logo h1 {
    font-size: 1.5rem;
    color: #3498db;
    margin: 0;
    font-weight: 700;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav-links a {
    text-decoration: none;
    color: #2c3e50;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-links a:hover,
.nav-links a.active {
    color: #3498db;
    background: rgba(52, 152, 219, 0.1);
}

.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* Mobile Navigation */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #2c3e50;
    cursor: pointer;
}

/* Hero Section Responsive */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 1rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hero-pattern" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23hero-pattern)"/></svg>');
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.hero p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    display: inline-block;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.cta-button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Featured Products Section */
.featured-products {
    padding: 4rem 1rem;
    background: white;
}

.featured-products h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 3rem;
    font-weight: 700;
}

/* Footer Responsive */
footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 1rem 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: #3498db;
    font-weight: 600;
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #3498db;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    margin-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Tablet Styles */
@media (min-width: 768px) {
    .container {
        padding: 0 2rem;
    }

    .navbar {
        padding: 1rem 2rem;
    }

    .logo h1 {
        font-size: 1.8rem;
    }

    .nav-links {
        gap: 1.5rem;
    }

    .hero {
        padding: 6rem 2rem;
    }

    .hero h1 {
        font-size: 3.5rem;
    }

    .hero p {
        font-size: 1.2rem;
    }

    .featured-products {
        padding: 6rem 2rem;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 3rem;
    }

    /* Products Grid - Tablet */
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    /* Profile Content - Tablet */
    .profile-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .profile-info {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .profile-stats {
        justify-content: center;
    }

    /* Services Grid - Tablet */
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .process-steps {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Desktop Styles */
@media (min-width: 1024px) {
    .navbar {
        padding: 1rem 3rem;
    }

    .logo h1 {
        font-size: 2rem;
    }

    .nav-links {
        gap: 2rem;
    }

    .hero {
        padding: 8rem 3rem;
    }

    .hero h1 {
        font-size: 4rem;
    }

    .hero p {
        font-size: 1.3rem;
    }

    .featured-products {
        padding: 8rem 3rem;
    }

    .footer-content {
        grid-template-columns: repeat(3, 1fr);
        gap: 4rem;
    }

    /* Products Grid - Desktop */
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .products-filters {
        flex-wrap: nowrap;
    }

    /* Profile Content - Desktop */
    .profile-content {
        grid-template-columns: 1fr 2fr;
    }

    .profile-info {
        flex-direction: row;
        text-align: left;
        gap: 2rem;
    }

    .profile-stats {
        justify-content: flex-start;
    }

    /* Services Grid - Desktop */
    .services-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .process-steps {
        grid-template-columns: repeat(4, 1fr);
    }

    .contact-buttons {
        flex-wrap: nowrap;
    }
}

/* Large Desktop Styles */
@media (min-width: 1440px) {
    .products-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .hero h1 {
        font-size: 4.5rem;
    }
}

/* Mobile-Specific Styles */
@media (max-width: 767px) {
    /* Mobile Navigation */
    .mobile-menu-toggle {
        display: block;
    }

    .nav-links {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        flex-direction: column;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .nav-links.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-links a {
        padding: 1rem;
        border-bottom: 1px solid #ecf0f1;
        width: 100%;
        text-align: center;
    }

    .nav-links a:last-child {
        border-bottom: none;
    }

    /* Mobile Hero */
    .hero {
        padding: 3rem 1rem;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }

    /* Mobile Products */
    .products-container {
        padding: 1rem;
    }

    .products-header h1 {
        font-size: 2rem;
    }

    .products-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box {
        max-width: none;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .product-card {
        margin: 0 auto;
        max-width: 350px;
    }

    /* Mobile Profile */
    .profile-container {
        padding: 1rem;
    }

    .profile-header {
        padding: 1.5rem;
    }

    .profile-info {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .profile-details h1 {
        font-size: 2rem;
    }

    .profile-stats {
        gap: 1rem;
        justify-content: center;
    }

    .profile-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .profile-tabs {
        flex-direction: column;
        padding: 0;
    }

    .tab-btn {
        border-radius: 0;
        border-bottom: 1px solid #ecf0f1;
    }

    .tab-btn:last-child {
        border-bottom: none;
    }

    /* Mobile Services */
    .services-container {
        padding: 1rem;
    }

    .services-hero {
        padding: 3rem 1rem;
        margin-bottom: 2rem;
    }

    .services-hero h1 {
        font-size: 2rem;
    }

    .services-hero p {
        font-size: 1rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-card {
        padding: 1.5rem;
    }

    .service-card.featured {
        transform: none;
    }

    .process-section {
        padding: 2rem 1rem;
        margin-bottom: 2rem;
    }

    .process-header h2 {
        font-size: 2rem;
    }

    .process-steps {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-cta {
        padding: 3rem 1rem;
    }

    .contact-cta h2 {
        font-size: 2rem;
    }

    .contact-buttons {
        flex-direction: column;
        align-items: center;
    }

    .contact-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    /* Mobile Forms */
    .form-actions {
        flex-direction: column;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
        text-align: center;
    }
}

/* Small Mobile Styles */
@media (max-width: 480px) {
    .hero h1 {
        font-size: 1.8rem;
    }

    .featured-products h2,
    .services-hero h1,
    .process-header h2,
    .faq-header h2,
    .contact-cta h2 {
        font-size: 1.8rem;
    }

    .product-card,
    .service-card,
    .profile-card {
        margin: 0;
    }

    .profile-avatar {
        width: 100px;
        height: 100px;
    }

    .profile-details h1 {
        font-size: 1.8rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .mobile-menu-toggle,
    .cta-button,
    .btn-add-cart,
    .btn-wishlist,
    .service-cta,
    .contact-btn,
    .btn-primary,
    .btn-secondary {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .hero,
    .services-hero,
    .profile-header,
    .contact-cta {
        background: white !important;
        color: black !important;
    }

    .product-card,
    .service-card,
    .profile-card {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}