/* Responsive Design - ShopEase E-commerce Application */

/* Tablet Styles (768px and up) */
@media (min-width: 768px) {
    /* Typography adjustments */
    .hero-title {
        font-size: 3.5rem;
    }
    
    .section-title {
        font-size: 2.5rem;
    }
    
    /* Hero section */
    .hero {
        padding: 4rem 0;
    }
    
    /* Features grid */
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    /* Products grid */
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    /* Newsletter form */
    .newsletter-form {
        max-width: 500px;
    }
    
    /* Footer */
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Desktop Styles (1024px and up) */
@media (min-width: 1024px) {
    /* Container padding */
    .container {
        padding: 0 var(--spacing-lg);
    }
    
    /* Navigation */
    .nav-container {
        padding: 0 var(--spacing-lg);
    }
    
    .nav-menu {
        gap: 2rem;
    }
    
    /* Typography */
    .hero-title {
        font-size: 4rem;
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
    }
    
    /* Hero section */
    .hero {
        padding: 6rem 0;
    }
    
    .hero-container {
        gap: 3rem;
    }
    
    /* Features */
    .features {
        padding: 4rem 0;
    }
    
    .features-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    /* Products */
    .featured-products {
        padding: 4rem 0;
    }
    
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    /* Newsletter */
    .newsletter {
        padding: 4rem 0;
    }
    
    .newsletter-form {
        max-width: 600px;
    }
    
    /* Footer */
    .footer {
        padding: 4rem 0 2rem;
    }
    
    .footer-content {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Large Desktop Styles (1440px and up) */
@media (min-width: 1440px) {
    /* Products grid */
    .products-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    /* Hero */
    .hero-title {
        font-size: 4.5rem;
    }
    
    /* Container max-width increase */
    .container,
    .nav-container,
    .hero-container {
        max-width: 1400px;
    }
}

/* Mobile Styles (767px and below) */
@media (max-width: 767px) {
    /* Show mobile menu toggle */
    .mobile-menu-toggle {
        display: block !important;
    }
    
    /* Hide desktop navigation */
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--bg-primary);
        flex-direction: column;
        padding: var(--spacing-xl);
        box-shadow: var(--shadow-lg);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-base);
        z-index: var(--z-dropdown);
    }
    
    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .nav-link {
        padding: var(--spacing-md);
        border-bottom: 1px solid var(--light-gray);
        width: 100%;
        text-align: center;
    }
    
    .nav-link:last-child {
        border-bottom: none;
    }
    
    /* Hide some nav actions on mobile */
    .search-toggle,
    .theme-toggle {
        display: none;
    }
    
    /* Hero section */
    .hero {
        padding: 3rem 0;
        text-align: center;
    }
    
    .hero-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .hero-actions {
        justify-content: center;
    }
    
    .hero-graphic {
        width: 150px;
        height: 150px;
    }
    
    .hero-graphic i {
        font-size: 3rem;
    }
    
    /* Sections */
    .section-title {
        font-size: var(--font-size-2xl);
    }
    
    .section-subtitle {
        font-size: var(--font-size-base);
    }
    
    /* Features */
    .features {
        padding: 3rem 0;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .feature-card {
        padding: var(--spacing-lg);
    }
    
    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }
    
    /* Products */
    .featured-products {
        padding: 3rem 0;
    }
    
    .products-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    /* Newsletter */
    .newsletter {
        padding: 3rem 0;
    }
    
    .newsletter-form {
        flex-direction: column;
        max-width: 100%;
    }
    
    .newsletter-form input {
        margin-bottom: var(--spacing-sm);
    }
    
    /* Footer */
    .footer {
        padding: 3rem 0 2rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }
    
    .footer-bottom {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }
    
    .footer-links {
        justify-content: center;
    }
    
    .social-links {
        justify-content: center;
    }
    
    /* Back to top button */
    .back-to-top {
        bottom: var(--spacing-md);
        right: var(--spacing-md);
        width: 45px;
        height: 45px;
    }
    
    /* Toast notifications */
    .toast-container {
        top: var(--spacing-md);
        right: var(--spacing-md);
        left: var(--spacing-md);
    }
    
    .toast {
        max-width: 100%;
    }
    
    /* Buttons */
    .btn {
        padding: var(--spacing-md) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }
    
    .hero-actions .btn {
        width: 100%;
        max-width: 200px;
    }
}

/* Small Mobile Styles (480px and below) */
@media (max-width: 480px) {
    /* Container padding */
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .nav-container {
        padding: 0 var(--spacing-sm);
    }
    
    /* Typography */
    .hero-title {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: var(--font-size-xl);
    }
    
    /* Logo */
    .logo h1 {
        font-size: var(--font-size-xl);
    }
    
    /* Hero */
    .hero {
        padding: 2rem 0;
    }
    
    .hero-graphic {
        width: 120px;
        height: 120px;
    }
    
    .hero-graphic i {
        font-size: 2.5rem;
    }
    
    /* Sections */
    .features,
    .featured-products,
    .newsletter {
        padding: 2rem 0;
    }
    
    /* Feature cards */
    .feature-card {
        padding: var(--spacing-md);
    }
    
    .feature-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-md);
    }
    
    /* Footer */
    .footer {
        padding: 2rem 0 1rem;
    }
    
    /* Back to top */
    .back-to-top {
        width: 40px;
        height: 40px;
        bottom: var(--spacing-sm);
        right: var(--spacing-sm);
    }
}

/* Print Styles */
@media print {
    /* Hide interactive elements */
    .nav-actions,
    .mobile-menu-toggle,
    .search-bar,
    .hero-actions,
    .newsletter,
    .back-to-top,
    .toast-container {
        display: none !important;
    }
    
    /* Adjust colors for print */
    body {
        background: white !important;
        color: black !important;
    }
    
    .hero {
        background: white !important;
        color: black !important;
    }
    
    .footer {
        background: white !important;
        color: black !important;
        border-top: 2px solid black;
    }
    
    /* Remove shadows and effects */
    .feature-card,
    .product-card {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
    
    /* Adjust typography */
    .hero-title,
    .section-title {
        color: black !important;
    }
    
    /* Page breaks */
    .hero,
    .features,
    .featured-products {
        page-break-inside: avoid;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .loading-spinner {
        animation: none;
    }
    
    .product-skeleton {
        animation: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0066cc;
        --primary-dark: #004499;
        --text-primary: #000000;
        --text-secondary: #333333;
        --bg-primary: #ffffff;
        --bg-secondary: #f5f5f5;
    }
    
    .btn {
        border: 2px solid currentColor;
    }
    
    .nav-link:hover,
    .nav-link.active {
        background: #000000;
        color: #ffffff;
    }
}

/* Focus Styles for Accessibility */
@media (prefers-reduced-motion: no-preference) {
    *:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }
    
    .btn:focus {
        outline-offset: 4px;
    }
}

/* Dark Mode Responsive Adjustments */
@media (prefers-color-scheme: dark) {
    [data-theme="auto"] {
        --bg-primary: #1a1a1a;
        --bg-secondary: #2d2d2d;
        --bg-dark: #121212;
        --text-primary: #ffffff;
        --text-secondary: #b0b0b0;
        --light-gray: #2d2d2d;
        --gray: #888888;
    }
}

/* Landscape Orientation on Mobile */
@media (max-width: 767px) and (orientation: landscape) {
    .hero {
        padding: 2rem 0;
    }
    
    .hero-container {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-lg);
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-graphic {
        width: 100px;
        height: 100px;
    }
}
