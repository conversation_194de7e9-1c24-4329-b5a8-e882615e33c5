<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shopping Cart - ShopEase</title>
    <meta name="description" content="Review your selected items and proceed to checkout">

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/svg+xml" href="../favicon.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="https://via.placeholder.com/32x32/3498db/ffffff?text=SE">
    <link rel="icon" type="image/png" sizes="16x16" href="https://via.placeholder.com/16x16/3498db/ffffff?text=SE">
    <link rel="apple-touch-icon" sizes="180x180" href="https://via.placeholder.com/180x180/3498db/ffffff?text=ShopEase">
    <meta name="theme-color" content="#3498db">

    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <link rel="stylesheet" href="../css/components.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Cart Page Specific Styles -->
    <style>
        .cart-page {
            min-height: 80vh;
            padding: var(--spacing-xl) 0;
        }
        
        .cart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-xl);
            padding-bottom: var(--spacing-lg);
            border-bottom: 2px solid var(--light-gray);
        }
        
        .cart-header h1 {
            margin: 0;
            color: var(--text-primary);
        }
        
        .cart-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: var(--spacing-xl);
        }
        
        .cart-items {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }
        
        .cart-item {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
            display: grid;
            grid-template-columns: 100px 1fr auto auto auto;
            gap: var(--spacing-lg);
            align-items: center;
            transition: box-shadow var(--transition-base);
        }
        
        .cart-item:hover {
            box-shadow: var(--shadow-md);
        }
        
        .item-image img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: var(--radius-md);
        }
        
        .item-details h4 {
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--text-primary);
        }
        
        .item-category {
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
            margin: 0 0 var(--spacing-xs) 0;
        }
        
        .item-price {
            font-weight: 600;
            color: var(--primary-color);
            margin: 0;
        }
        
        .item-quantity {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
        }
        
        .quantity-input {
            width: 60px;
            padding: var(--spacing-xs);
            border: 2px solid var(--light-gray);
            border-radius: var(--radius-md);
            text-align: center;
            font-weight: 600;
        }
        
        .item-total {
            text-align: center;
        }
        
        .total-price {
            font-size: var(--font-size-lg);
            font-weight: 700;
            color: var(--success-color);
        }
        
        .item-actions {
            display: flex;
            justify-content: center;
        }
        
        .remove-item {
            background: var(--accent-color);
            color: var(--white);
            border: none;
            padding: var(--spacing-sm);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all var(--transition-fast);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .remove-item:hover {
            background: #c0392b;
            transform: scale(1.1);
        }
        
        .cart-summary {
            position: sticky;
            top: var(--spacing-xl);
            height: fit-content;
        }
        
        .summary-card {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-md);
        }
        
        .summary-card h3 {
            margin: 0 0 var(--spacing-lg) 0;
            color: var(--text-primary);
            text-align: center;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm) 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .summary-row:last-of-type {
            border-bottom: none;
        }
        
        .summary-row.total {
            font-size: var(--font-size-lg);
            font-weight: 700;
            color: var(--text-primary);
            border-top: 2px solid var(--light-gray);
            margin-top: var(--spacing-md);
            padding-top: var(--spacing-md);
        }
        
        .checkout-btn {
            width: 100%;
            margin: var(--spacing-lg) 0 var(--spacing-md) 0;
        }
        
        .empty-state {
            text-align: center;
            padding: var(--spacing-xxl);
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .cart-content {
                grid-template-columns: 1fr;
            }
            
            .cart-item {
                grid-template-columns: 80px 1fr;
                gap: var(--spacing-md);
            }
            
            .item-quantity,
            .item-total,
            .item-actions {
                grid-column: 1 / -1;
                justify-self: center;
                margin-top: var(--spacing-sm);
            }
            
            .item-quantity {
                flex-direction: row;
                gap: var(--spacing-sm);
            }
            
            .cart-summary {
                position: static;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p>Loading Cart...</p>
    </div>

    <!-- Header Section -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <h1><i class="fas fa-shopping-bag"></i> ShopEase</h1>
                </div>
                
                <div class="nav-menu" id="nav-menu">
                    <a href="../index.html" class="nav-link">Home</a>
                    <a href="products.html" class="nav-link">Products</a>
                    <a href="cart.html" class="nav-link active">
                        <i class="fas fa-shopping-cart"></i>
                        Cart <span class="cart-count" id="cart-count">0</span>
                    </a>
                    <a href="profile.html" class="nav-link">
                        <i class="fas fa-user"></i>
                        Profile
                    </a>
                </div>
                
                <div class="nav-actions">
                    <button class="search-toggle" id="search-toggle">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="theme-toggle" id="theme-toggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
            
            <!-- Search Bar -->
            <div class="search-bar" id="search-bar">
                <div class="search-container">
                    <input type="text" placeholder="Search products..." id="search-input">
                    <button class="search-btn" id="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Cart Page Content -->
    <main class="cart-page">
        <div class="container">
            <div id="cart-container">
                <!-- Cart content will be loaded here by JavaScript -->
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h3 class="empty-state-title">Loading your cart...</h3>
                    <p class="empty-state-description">Please wait while we load your items.</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>ShopEase</h3>
                    <p>Your trusted online shopping destination for quality products at amazing prices.</p>
                    <div class="social-links">
                        <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                        <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
                        <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                        <a href="#" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="cart.html">Cart</a></li>
                        <li><a href="profile.html">Profile</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Customer Service</h4>
                    <ul>
                        <li><a href="#">Contact Us</a></li>
                        <li><a href="#">FAQ</a></li>
                        <li><a href="#">Shipping Info</a></li>
                        <li><a href="#">Returns</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-phone"></i> +254 758 175 275</p>
                        <p><i class="fas fa-map-marker-alt"></i> Nairobi, Kenya</p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 ShopEase. All rights reserved.</p>
                <div class="footer-links">
                    <a href="#">Privacy Policy</a>
                    <a href="#">Terms of Service</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="back-to-top" aria-label="Back to top">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toast-container"></div>

    <!-- JavaScript Files -->
    <script src="../js/utils.js"></script>
    <script src="../js/main.js"></script>
    <script src="../js/products.js"></script>
    <script src="../js/cart.js"></script>
</body>
</html>
