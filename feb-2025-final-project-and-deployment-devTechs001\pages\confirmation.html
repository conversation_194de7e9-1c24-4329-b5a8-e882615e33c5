<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - ShopEase</title>
    <meta name="description" content="Your order has been confirmed - ShopEase">
    
    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/svg+xml" href="../favicon.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="https://via.placeholder.com/32x32/3498db/ffffff?text=SE">
    <link rel="icon" type="image/png" sizes="16x16" href="https://via.placeholder.com/16x16/3498db/ffffff?text=SE">
    <link rel="apple-touch-icon" sizes="180x180" href="https://via.placeholder.com/180x180/3498db/ffffff?text=ShopEase">
    <meta name="theme-color" content="#3498db">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <link rel="stylesheet" href="../css/components.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Confirmation Page Specific Styles -->
    <style>
        .confirmation-page {
            min-height: 80vh;
            padding: var(--spacing-xl) 0;
            background: var(--bg-secondary);
        }
        
        .confirmation-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
        }
        
        .confirmation-header {
            text-align: center;
            margin-bottom: var(--spacing-xxl);
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xxl);
            box-shadow: var(--shadow-md);
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            background: var(--success-color);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--spacing-lg);
            color: var(--white);
            font-size: 2rem;
            animation: successPulse 2s ease-in-out;
        }
        
        @keyframes successPulse {
            0% { transform: scale(0.8); opacity: 0; }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); opacity: 1; }
        }
        
        .confirmation-header h1 {
            color: var(--success-color);
            margin-bottom: var(--spacing-md);
        }
        
        .order-number {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-primary);
            background: var(--bg-secondary);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            display: inline-block;
            margin-top: var(--spacing-md);
        }
        
        .order-details {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-md);
            margin-bottom: var(--spacing-xl);
        }
        
        .order-details h3 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .order-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md) 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .order-item:last-child {
            border-bottom: none;
        }
        
        .item-image {
            width: 60px;
            height: 60px;
            border-radius: var(--radius-md);
            object-fit: cover;
        }
        
        .item-details {
            flex: 1;
        }
        
        .item-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .item-quantity {
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
        }
        
        .item-price {
            font-weight: 600;
            color: var(--success-color);
        }
        
        .order-summary {
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm) 0;
        }
        
        .summary-row.total {
            font-size: var(--font-size-lg);
            font-weight: 700;
            color: var(--text-primary);
            border-top: 2px solid var(--light-gray);
            margin-top: var(--spacing-md);
            padding-top: var(--spacing-md);
        }
        
        .shipping-info {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-md);
            margin-bottom: var(--spacing-xl);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xs);
        }
        
        .info-label {
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
            font-weight: 500;
        }
        
        .info-value {
            color: var(--text-primary);
            font-weight: 600;
        }
        
        .next-steps {
            background: var(--bg-primary);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-md);
            margin-bottom: var(--spacing-xl);
        }
        
        .steps-list {
            list-style: none;
            padding: 0;
        }
        
        .steps-list li {
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .step-number {
            width: 24px;
            height: 24px;
            background: var(--primary-color);
            color: var(--white);
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: var(--font-size-sm);
            flex-shrink: 0;
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-md);
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-print {
            background: var(--gray);
            color: var(--white);
        }
        
        .btn-print:hover {
            background: var(--dark-gray);
        }
        
        @media (max-width: 768px) {
            .action-buttons {
                flex-direction: column;
            }
            
            .action-buttons .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p>Loading Confirmation...</p>
    </div>

    <!-- Header Section -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <h1><i class="fas fa-shopping-bag"></i> ShopEase</h1>
                </div>
                
                <div class="nav-menu" id="nav-menu" aria-hidden="true">
                    <a href="../index.html" class="nav-link">Home</a>
                    <a href="products.html" class="nav-link">Products</a>
                    <a href="cart.html" class="nav-link">
                        <i class="fas fa-shopping-cart"></i>
                        Cart <span class="cart-count" id="cart-count">0</span>
                    </a>
                    <a href="profile.html" class="nav-link">
                        <i class="fas fa-user"></i>
                        Profile
                    </a>
                </div>
                
                <div class="nav-actions">
                    <button type="button" class="search-toggle" id="search-toggle">
                        <i class="fas fa-search"></i>
                    </button>
                    <button type="button" class="theme-toggle" id="theme-toggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button type="button" class="mobile-menu-toggle" id="mobile-menu-toggle" aria-expanded="false" aria-controls="nav-menu" aria-label="Toggle navigation menu">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Confirmation Page Content -->
    <main class="confirmation-page">
        <div class="confirmation-container">
            <!-- Confirmation Header -->
            <div class="confirmation-header">
                <div class="success-icon">
                    <i class="fas fa-check"></i>
                </div>
                <h1>Order Confirmed!</h1>
                <p>Thank you for your purchase. Your order has been successfully placed and is being processed.</p>
                <div class="order-number" id="order-number">
                    Order #: Loading...
                </div>
            </div>

            <!-- Order Details -->
            <div class="order-details">
                <h3>
                    <i class="fas fa-box"></i>
                    Order Details
                </h3>
                <div id="order-items">
                    <!-- Order items will be loaded here -->
                </div>
                <div class="order-summary">
                    <div class="summary-row">
                        <span>Subtotal</span>
                        <span id="subtotal">$0.00</span>
                    </div>
                    <div class="summary-row">
                        <span>Shipping</span>
                        <span id="shipping">$0.00</span>
                    </div>
                    <div class="summary-row">
                        <span>Tax</span>
                        <span id="tax">$0.00</span>
                    </div>
                    <div class="summary-row total">
                        <span>Total</span>
                        <span id="total">$0.00</span>
                    </div>
                </div>
            </div>

            <!-- Shipping Information -->
            <div class="shipping-info">
                <h3>
                    <i class="fas fa-shipping-fast"></i>
                    Shipping Information
                </h3>
                <div class="info-grid" id="shipping-details">
                    <!-- Shipping details will be loaded here -->
                </div>
            </div>

            <!-- Next Steps -->
            <div class="next-steps">
                <h3>
                    <i class="fas fa-list-check"></i>
                    What's Next?
                </h3>
                <ol class="steps-list">
                    <li>
                        <div class="step-number">1</div>
                        <div>
                            <strong>Order Processing</strong><br>
                            We'll prepare your items for shipment within 1-2 business days.
                        </div>
                    </li>
                    <li>
                        <div class="step-number">2</div>
                        <div>
                            <strong>Shipping Notification</strong><br>
                            You'll receive an email with tracking information once your order ships.
                        </div>
                    </li>
                    <li>
                        <div class="step-number">3</div>
                        <div>
                            <strong>Delivery</strong><br>
                            Your order will arrive within 3-7 business days depending on your location.
                        </div>
                    </li>
                </ol>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button type="button" class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print"></i>
                    Print Order
                </button>
                <a href="products.html" class="btn btn-outline">
                    <i class="fas fa-shopping-bag"></i>
                    Continue Shopping
                </a>
                <a href="profile.html" class="btn btn-secondary">
                    <i class="fas fa-user"></i>
                    View Profile
                </a>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>ShopEase</h3>
                    <p>Your trusted online shopping destination for quality products at amazing prices.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="products.html">Products</a></li>
                        <li><a href="cart.html">Cart</a></li>
                        <li><a href="profile.html">Profile</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 ShopEase. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="back-to-top" aria-label="Back to top">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toast-container"></div>

    <!-- JavaScript Files -->
    <script src="../js/utils.js"></script>
    <script src="../js/main.js"></script>
    <script src="../js/products.js"></script>
    <script src="../js/cart.js"></script>
    
    <!-- Confirmation Page Script -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            loadOrderConfirmation();
        });

        function loadOrderConfirmation() {
            // Get order ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const orderId = urlParams.get('order');

            if (!orderId) {
                // Redirect to home if no order ID
                window.location.href = '../index.html';
                return;
            }

            // Load order from storage
            const orders = StorageManager.get('user_orders', []);
            const order = orders.find(o => o.orderId === orderId);

            if (!order) {
                // Order not found
                document.querySelector('.confirmation-header h1').textContent = 'Order Not Found';
                document.querySelector('.confirmation-header p').textContent = 'The order you are looking for could not be found.';
                return;
            }

            // Display order details
            displayOrderDetails(order);
        }

        function displayOrderDetails(order) {
            // Update order number
            document.getElementById('order-number').textContent = `Order #: ${order.orderId}`;

            // Display order items
            const orderItemsContainer = document.getElementById('order-items');
            orderItemsContainer.innerHTML = order.items.map(item => `
                <div class="order-item">
                    <img src="${item.image}" alt="${item.name}" class="item-image">
                    <div class="item-details">
                        <div class="item-name">${item.name}</div>
                        <div class="item-quantity">Qty: ${item.quantity}</div>
                    </div>
                    <div class="item-price">${Utils.formatCurrency(item.price * item.quantity)}</div>
                </div>
            `).join('');

            // Update totals
            document.getElementById('subtotal').textContent = Utils.formatCurrency(order.totals.subtotal);
            document.getElementById('shipping').textContent = order.totals.shipping === 0 ? 'FREE' : Utils.formatCurrency(order.totals.shipping);
            document.getElementById('tax').textContent = Utils.formatCurrency(order.totals.tax);
            document.getElementById('total').textContent = Utils.formatCurrency(order.totals.total);

            // Display shipping information
            const shippingContainer = document.getElementById('shipping-details');
            const shipping = order.shipping;
            shippingContainer.innerHTML = `
                <div class="info-item">
                    <div class="info-label">Name</div>
                    <div class="info-value">${shipping.firstName} ${shipping.lastName}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Email</div>
                    <div class="info-value">${shipping.email}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Phone</div>
                    <div class="info-value">${shipping.phone}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Address</div>
                    <div class="info-value">${shipping.address}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">City</div>
                    <div class="info-value">${shipping.city}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Country</div>
                    <div class="info-value">${shipping.country}</div>
                </div>
            `;
        }
    </script>
</body>
</html>
