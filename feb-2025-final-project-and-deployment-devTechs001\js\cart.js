// Cart Management - ShopEase E-commerce Application

/**
 * Cart Manager Class
 */
class CartManager {
    constructor() {
        this.cart = this.loadCart();
        this.init();
    }

    /**
     * Initialize cart manager
     */
    init() {
        this.bindEvents();
        this.updateCartDisplay();
    }

    /**
     * Load cart from localStorage
     */
    loadCart() {
        const cart = StorageManager.get('shopease_cart', []);

        // Validate cart data structure
        if (Array.isArray(cart)) {
            return cart.filter(item =>
                item &&
                typeof item === 'object' &&
                item.id &&
                item.name &&
                typeof item.price === 'number' &&
                typeof item.quantity === 'number' &&
                item.quantity > 0
            );
        }
        return [];
    }

    /**
     * Save cart to localStorage
     */
    saveCart() {
        // Validate cart before saving
        const validCart = this.cart.filter(item =>
            item &&
            typeof item === 'object' &&
            item.id &&
            item.name &&
            typeof item.price === 'number' &&
            typeof item.quantity === 'number' &&
            item.quantity > 0
        );

        this.cart = validCart;

        const success = StorageManager.set('shopease_cart', this.cart);

        if (success) {
            // Trigger storage event for other tabs
            window.dispatchEvent(new StorageEvent('storage', {
                key: 'shopease_cart',
                newValue: JSON.stringify(this.cart),
                storageArea: localStorage
            }));

            this.updateCartDisplay();

            // Save cart timestamp
            StorageManager.set('shopease_cart_updated', new Date().toISOString());
        } else {
            if (window.shopEaseApp) {
                window.shopEaseApp.showToast('Failed to save cart changes', 'error');
            }
        }
    }

    /**
     * Add item to cart
     * @param {object} product - Product to add
     * @param {number} quantity - Quantity to add (default: 1)
     */
    addItem(product, quantity = 1) {
        const existingItem = this.cart.find(item => item.id === product.id);

        if (existingItem) {
            existingItem.quantity += quantity;
        } else {
            this.cart.push({
                id: product.id,
                name: product.name,
                price: product.price,
                image: product.image,
                category: product.category,
                quantity: quantity,
                addedAt: new Date().toISOString()
            });
        }

        this.saveCart();
        this.trackEvent('add_to_cart', product);
    }

    /**
     * Remove item from cart
     * @param {string} productId - Product ID to remove
     */
    removeItem(productId) {
        const itemIndex = this.cart.findIndex(item => item.id === productId);
        
        if (itemIndex > -1) {
            const removedItem = this.cart[itemIndex];
            this.cart.splice(itemIndex, 1);
            this.saveCart();
            this.trackEvent('remove_from_cart', removedItem);
            
            if (window.shopEaseApp) {
                window.shopEaseApp.showToast(`${removedItem.name} removed from cart`, 'info');
            }
        }
    }

    /**
     * Update item quantity
     * @param {string} productId - Product ID
     * @param {number} quantity - New quantity
     */
    updateQuantity(productId, quantity) {
        const item = this.cart.find(item => item.id === productId);
        
        if (item) {
            if (quantity <= 0) {
                this.removeItem(productId);
            } else {
                item.quantity = quantity;
                this.saveCart();
                this.trackEvent('update_quantity', item);
            }
        }
    }

    /**
     * Clear entire cart
     */
    clearCart() {
        this.cart = [];
        this.saveCart();
        this.trackEvent('clear_cart');
        
        if (window.shopEaseApp) {
            window.shopEaseApp.showToast('Cart cleared', 'info');
        }
    }

    /**
     * Get cart items
     */
    getItems() {
        return [...this.cart];
    }

    /**
     * Get item count
     */
    getItemCount() {
        return this.cart.reduce((total, item) => total + item.quantity, 0);
    }

    /**
     * Get cart total
     */
    getTotal() {
        return this.cart.reduce((total, item) => total + (item.price * item.quantity), 0);
    }

    /**
     * Get cart subtotal (before tax and shipping)
     */
    getSubtotal() {
        return this.getTotal();
    }

    /**
     * Calculate tax
     * @param {number} rate - Tax rate (default: 0.08 = 8%)
     */
    getTax(rate = 0.08) {
        return this.getSubtotal() * rate;
    }

    /**
     * Calculate shipping
     * @param {number} freeShippingThreshold - Free shipping threshold (default: 50)
     */
    getShipping(freeShippingThreshold = 50) {
        const subtotal = this.getSubtotal();
        return subtotal >= freeShippingThreshold ? 0 : 9.99;
    }

    /**
     * Get final total (including tax and shipping)
     */
    getFinalTotal() {
        return this.getSubtotal() + this.getTax() + this.getShipping();
    }

    /**
     * Check if cart is empty
     */
    isEmpty() {
        return this.cart.length === 0;
    }

    /**
     * Bind events
     */
    bindEvents() {
        // Listen for cart updates from other tabs
        window.addEventListener('storage', (e) => {
            if (e.key === 'shopease_cart') {
                this.cart = this.loadCart();
                this.updateCartDisplay();
            }
        });

        // Bind cart page events if on cart page
        if (window.location.pathname.includes('cart.html')) {
            this.bindCartPageEvents();
        }
    }

    /**
     * Bind cart page specific events
     */
    bindCartPageEvents() {
        // Quantity change events
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('quantity-input')) {
                const productId = e.target.getAttribute('data-product-id');
                const quantity = parseInt(e.target.value);
                this.updateQuantity(productId, quantity);
            }
        });

        // Remove item events
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('remove-item') || e.target.closest('.remove-item')) {
                e.preventDefault();
                const button = e.target.closest('.remove-item') || e.target;
                const productId = button.getAttribute('data-product-id');
                this.removeItem(productId);
            }

            if (e.target.classList.contains('clear-cart') || e.target.closest('.clear-cart')) {
                e.preventDefault();
                this.showClearCartConfirmation();
            }
        });

        // Checkout button
        const checkoutBtn = document.getElementById('checkout-btn');
        if (checkoutBtn) {
            checkoutBtn.addEventListener('click', () => {
                this.proceedToCheckout();
            });
        }
    }

    /**
     * Update cart display
     */
    updateCartDisplay() {
        this.updateCartCount();
        this.updateCartPage();
    }

    /**
     * Update cart count in navigation
     */
    updateCartCount() {
        const cartCountElements = document.querySelectorAll('.cart-count');
        const itemCount = this.getItemCount();
        
        cartCountElements.forEach(element => {
            element.textContent = itemCount;
            element.style.display = itemCount > 0 ? 'inline' : 'none';
        });
    }

    /**
     * Update cart page content
     */
    updateCartPage() {
        if (!window.location.pathname.includes('cart.html')) return;

        const cartContainer = document.getElementById('cart-container');
        if (!cartContainer) return;

        if (this.isEmpty()) {
            this.showEmptyCart(cartContainer);
        } else {
            this.showCartItems(cartContainer);
        }
    }

    /**
     * Show empty cart state
     */
    showEmptyCart(container) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h3 class="empty-state-title">Your cart is empty</h3>
                <p class="empty-state-description">
                    Looks like you haven't added any items to your cart yet.
                </p>
                <a href="../pages/products.html" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i>
                    Continue Shopping
                </a>
            </div>
        `;
    }

    /**
     * Show cart items
     */
    showCartItems(container) {
        const cartHTML = `
            <div class="cart-header">
                <h1>Shopping Cart</h1>
                <button class="btn btn-outline clear-cart">
                    <i class="fas fa-trash"></i>
                    Clear Cart
                </button>
            </div>
            
            <div class="cart-content">
                <div class="cart-items">
                    ${this.cart.map(item => this.createCartItemHTML(item)).join('')}
                </div>
                
                <div class="cart-summary">
                    <div class="summary-card">
                        <h3>Order Summary</h3>
                        <div class="summary-row">
                            <span>Subtotal (${this.getItemCount()} items)</span>
                            <span>${Utils.formatCurrency(this.getSubtotal())}</span>
                        </div>
                        <div class="summary-row">
                            <span>Tax</span>
                            <span>${Utils.formatCurrency(this.getTax())}</span>
                        </div>
                        <div class="summary-row">
                            <span>Shipping</span>
                            <span>${this.getShipping() === 0 ? 'FREE' : Utils.formatCurrency(this.getShipping())}</span>
                        </div>
                        <div class="summary-row total">
                            <span>Total</span>
                            <span>${Utils.formatCurrency(this.getFinalTotal())}</span>
                        </div>
                        <button class="btn btn-primary checkout-btn" id="checkout-btn">
                            <i class="fas fa-credit-card"></i>
                            Proceed to Checkout
                        </button>
                        <a href="../pages/products.html" class="btn btn-outline">
                            <i class="fas fa-arrow-left"></i>
                            Continue Shopping
                        </a>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = cartHTML;
    }

    /**
     * Create cart item HTML
     */
    createCartItemHTML(item) {
        return `
            <div class="cart-item" data-product-id="${item.id}">
                <div class="item-image">
                    <img src="${item.image}" alt="${item.name}">
                </div>
                <div class="item-details">
                    <h4 class="item-name">${item.name}</h4>
                    <p class="item-category">${item.category}</p>
                    <p class="item-price">${Utils.formatCurrency(item.price)}</p>
                </div>
                <div class="item-quantity">
                    <label for="qty-${item.id}">Quantity:</label>
                    <input 
                        type="number" 
                        id="qty-${item.id}"
                        class="quantity-input" 
                        data-product-id="${item.id}"
                        value="${item.quantity}" 
                        min="1" 
                        max="99"
                    >
                </div>
                <div class="item-total">
                    <span class="total-price">${Utils.formatCurrency(item.price * item.quantity)}</span>
                </div>
                <div class="item-actions">
                    <button class="remove-item" data-product-id="${item.id}" title="Remove item">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Show clear cart confirmation
     */
    showClearCartConfirmation() {
        if (confirm('Are you sure you want to clear your cart? This action cannot be undone.')) {
            this.clearCart();
        }
    }

    /**
     * Proceed to checkout
     */
    proceedToCheckout() {
        if (this.isEmpty()) {
            if (window.shopEaseApp) {
                window.shopEaseApp.showToast('Your cart is empty', 'warning');
            }
            return;
        }

        // In a real application, this would redirect to checkout page
        if (window.shopEaseApp) {
            window.shopEaseApp.showToast('Checkout functionality coming soon!', 'info');
        }

        // Track checkout event
        this.trackEvent('begin_checkout');
    }

    /**
     * Track events for analytics
     */
    trackEvent(eventName, data = null) {
        // In a real application, this would send data to analytics service
        console.log('Cart Event:', eventName, data);

        // Store event using StorageManager
        const events = StorageManager.get('cart_events', []);
        events.push({
            event: eventName,
            data: data,
            timestamp: new Date().toISOString(),
            sessionId: this.getSessionId()
        });

        // Keep only last 100 events
        if (events.length > 100) {
            events.splice(0, events.length - 100);
        }

        StorageManager.set('cart_events', events);
    }

    /**
     * Get or create session ID
     */
    getSessionId() {
        let sessionId = StorageManager.get('session_id');
        if (!sessionId) {
            sessionId = Utils.generateId();
            StorageManager.set('session_id', sessionId);
        }
        return sessionId;
    }

    /**
     * Get cart analytics
     */
    getAnalytics() {
        const events = StorageManager.get('cart_events', []);
        const sessionEvents = events.filter(event => event.sessionId === this.getSessionId());

        return {
            itemCount: this.getItemCount(),
            total: this.getTotal(),
            averageItemValue: this.getItemCount() > 0 ? this.getTotal() / this.getItemCount() : 0,
            categories: [...new Set(this.cart.map(item => item.category))],
            events: events,
            sessionEvents: sessionEvents,
            sessionId: this.getSessionId(),
            cartAge: this.getCartAge(),
            lastUpdated: StorageManager.get('shopease_cart_updated')
        };
    }

    /**
     * Get cart age in minutes
     */
    getCartAge() {
        const lastUpdated = StorageManager.get('shopease_cart_updated');
        if (!lastUpdated) return 0;

        const now = new Date();
        const updated = new Date(lastUpdated);
        return Math.floor((now - updated) / (1000 * 60));
    }

    /**
     * Export cart data
     */
    exportCart() {
        const cartData = {
            items: this.cart,
            summary: {
                itemCount: this.getItemCount(),
                subtotal: this.getSubtotal(),
                tax: this.getTax(),
                shipping: this.getShipping(),
                total: this.getFinalTotal()
            },
            exportedAt: new Date().toISOString()
        };

        const dataStr = JSON.stringify(cartData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        Utils.downloadFile(url, `shopease-cart-${new Date().toISOString().split('T')[0]}.json`);
        
        URL.revokeObjectURL(url);
    }

    /**
     * Import cart data
     */
    async importCart(file) {
        try {
            const text = await file.text();
            const data = JSON.parse(text);
            
            if (data.items && Array.isArray(data.items)) {
                this.cart = data.items;
                this.saveCart();
                
                if (window.shopEaseApp) {
                    window.shopEaseApp.showToast('Cart imported successfully!', 'success');
                }
            } else {
                throw new Error('Invalid cart file format');
            }
        } catch (error) {
            console.error('Error importing cart:', error);
            if (window.shopEaseApp) {
                window.shopEaseApp.showToast('Failed to import cart', 'error');
            }
        }
    }
}

// Initialize CartManager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.CartManager = CartManager;
    window.cartManager = new CartManager();
});
